export const IconFilter = () => (
  <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.03492 0C2.53529 0 2.10329 2.32253e-07 1.76402 0.0331037C1.42911 0.0640003 1.01674 0.137931 0.680745 0.417104C0.468318 0.594757 0.297195 0.817568 0.179541 1.06969C0.0618877 1.32182 0.000594135 1.59707 1.86316e-05 1.87586C-0.00216318 2.30621 0.187655 2.67697 0.370927 2.97049C0.5542 3.26842 0.81929 3.62152 1.12911 4.03421L3.96111 7.81022C4.23601 8.17656 4.30474 8.27367 4.35274 8.37298C4.4022 8.47597 4.4382 8.58521 4.46074 8.7007C4.48256 8.81105 4.48583 8.93463 4.48583 9.40029V12.6775C4.48583 12.9402 4.48474 13.2226 4.5622 13.4919C4.62983 13.7258 4.7422 13.9443 4.89274 14.1374C5.06729 14.3581 5.29638 14.5192 5.50583 14.6637L5.5582 14.7002L6.63819 15.4538C6.81928 15.5807 6.99928 15.7076 7.15638 15.7959C7.31347 15.8842 7.57965 16.0188 7.9091 15.9978C8.3051 15.9735 8.66183 15.7683 8.89092 15.4516C9.07855 15.1912 9.12001 14.901 9.13528 14.72C9.15055 14.5368 9.15055 14.3117 9.15055 14.0822V9.40029C9.15055 8.93463 9.15492 8.81105 9.17565 8.7007C9.19746 8.58521 9.23383 8.47597 9.28474 8.37298C9.33165 8.27367 9.40037 8.17656 9.67528 7.81022L12.5073 4.03421C12.8171 3.62152 13.0822 3.26842 13.2666 2.97049C13.4487 2.67697 13.6375 2.30621 13.6364 1.87586C13.6358 1.59707 13.5745 1.32182 13.4568 1.06969C13.3392 0.817568 13.1681 0.594757 12.9556 0.417104C12.6196 0.137931 12.2073 0.0639999 11.8724 0.0319998C11.5331 -2.13755e-07 11.1011 0 10.6015 0H3.03492ZM1.63747 1.86373C1.641 1.80873 1.66293 1.75658 1.69965 1.71586C1.72584 1.70814 1.7902 1.69269 1.91783 1.68055C2.17093 1.65628 2.52438 1.65517 3.0742 1.65517H10.5622C11.112 1.65517 11.4655 1.65517 11.7186 1.68055C11.8462 1.69159 11.9106 1.70814 11.9367 1.71586C11.9695 1.75228 11.9946 1.80414 11.9989 1.86373C11.9679 1.94436 11.9276 2.02102 11.8789 2.09214C11.7393 2.31835 11.5189 2.61297 11.1807 3.06428L10.7891 3.58621H2.84729L2.45456 3.06428C2.11638 2.61187 1.89602 2.31835 1.75638 2.09214C1.70767 2.02102 1.66848 1.94436 1.63747 1.86373ZM13.9091 5.51725C13.6921 5.51725 13.484 5.60444 13.3306 5.75964C13.1771 5.91485 13.0909 6.12535 13.0909 6.34484C13.0909 6.56433 13.1771 6.77483 13.3306 6.93003C13.484 7.08523 13.6921 7.17242 13.9091 7.17242H17.1818C17.3988 7.17242 17.6069 7.08523 17.7604 6.93003C17.9138 6.77483 18 6.56433 18 6.34484C18 6.12535 17.9138 5.91485 17.7604 5.75964C17.6069 5.60444 17.3988 5.51725 17.1818 5.51725H13.9091ZM12.2727 8.27587C12.0557 8.27587 11.8476 8.36307 11.6942 8.51827C11.5408 8.67347 11.4546 8.88397 11.4546 9.10346C11.4546 9.32295 11.5408 9.53345 11.6942 9.68865C11.8476 9.84386 12.0557 9.93105 12.2727 9.93105H17.1818C17.3988 9.93105 17.6069 9.84386 17.7604 9.68865C17.9138 9.53345 18 9.32295 18 9.10346C18 8.88397 17.9138 8.67347 17.7604 8.51827C17.6069 8.36307 17.3988 8.27587 17.1818 8.27587H12.2727ZM11.7273 11.0345C11.5103 11.0345 11.3022 11.1217 11.1487 11.2769C10.9953 11.4321 10.9091 11.6426 10.9091 11.8621C10.9091 12.0816 10.9953 12.2921 11.1487 12.4473C11.3022 12.6025 11.5103 12.6897 11.7273 12.6897H17.1818C17.3988 12.6897 17.6069 12.6025 17.7604 12.4473C17.9138 12.2921 18 12.0816 18 11.8621C18 11.6426 17.9138 11.4321 17.7604 11.2769C17.6069 11.1217 17.3988 11.0345 17.1818 11.0345H11.7273ZM11.7273 13.7931C11.5103 13.7931 11.3022 13.8803 11.1487 14.0355C10.9953 14.1907 10.9091 14.4012 10.9091 14.6207C10.9091 14.8402 10.9953 15.0507 11.1487 15.2059C11.3022 15.3611 11.5103 15.4483 11.7273 15.4483H14.4545C14.6715 15.4483 14.8797 15.3611 15.0331 15.2059C15.1865 15.0507 15.2727 14.8402 15.2727 14.6207C15.2727 14.4012 15.1865 14.1907 15.0331 14.0355C14.8797 13.8803 14.6715 13.7931 14.4545 13.7931H11.7273Z"
      fill="#008979"
    />
  </svg>
)
