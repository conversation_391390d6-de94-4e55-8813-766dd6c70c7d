import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { RouterProvider, createRouter } from '@tanstack/react-router'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'

import { routeTree } from './routeTree.gen'

import { LoadScript } from '@react-google-maps/api'
import './index.css'

const router = createRouter({ routeTree })
const queryClient = new QueryClient()

const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <LoadScript googleMapsApiKey={apiKey} onLoad={() => console.log('Maps API has loaded.')}>
        <RouterProvider router={router} />
      </LoadScript>
    </QueryClientProvider>
  </StrictMode>
)
