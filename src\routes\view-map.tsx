import { IS_AUTHENTICATED } from '@/constants'
import ROUTE from '@/constants/route'
import { createFileRoute, FileRoutesByPath, redirect } from '@tanstack/react-router'
import { lazy } from 'react'

const ViewMap = lazy(() => import('@/pages/ViewMap'))

export const Route = createFileRoute(ROUTE.VIEW_MAP as keyof FileRoutesByPath)({
  beforeLoad: () => {
    const isAuthenticated = localStorage.getItem(IS_AUTHENTICATED)
    if (!isAuthenticated) {
      throw redirect({
        to: ROUTE.LOGIN,
      })
    }
  },
  component: ViewMap,
})
