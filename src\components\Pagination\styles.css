.pagination {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 8px;
  align-items: center;
}

.page-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-link {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: #4a4a4a;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  border-radius: 50%;
  border: 1px solid #f1f1f1;
}

.page-item:not(.active) .page-link:hover {
  background-color: #f3f4f6;
}

.page-item.active .page-link {
  background-color: var(--color-primary);
  color: white;
}

.page-item.disabled .page-link {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #e9e9e9;
}

/* Previous and Next buttons */
.page-item:first-child .page-link,
.page-item:last-child .page-link {
  padding: 8px;
}
