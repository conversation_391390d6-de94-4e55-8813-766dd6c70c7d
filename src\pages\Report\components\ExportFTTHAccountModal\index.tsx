import { getViewMapReportFtthAccountList } from '@/api'
import Button, { ButtonVariant } from '@/components/Button'
import CustomerDataExporter from '@/components/ExportDataButton'
import Modal from '@/components/Modal'
import Spinner from '@/components/Spinner'
import { EViewMapReportStatusFTTH } from '@/types'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useMemo } from 'react'
import { ModalInfo } from '../..'

const STATUS_MAPPING: Record<EViewMapReportStatusFTTH, string> = {
  0: 'Newly activated',
  1: 'Blocked for 1 month',
  2: 'Blocked for 2 months',
  3: 'Blocked for 3 months',
  4: 'Blocked over 3 months',
}

function ExportFTTHAccountModal({
  isOpen,
  onCloseModal,
  modalInfo,
}: {
  isOpen: boolean
  onCloseModal: () => void
  modalInfo: ModalInfo | null
}) {
  const { refetch, isLoading, data } = useQuery({
    queryKey: [
      'reportFtthAccountsDetail',
      {
        params: {
          province: modalInfo?.province?.code || '',
          status: (modalInfo?.status as EViewMapReportStatusFTTH) || 0,
          reportType: modalInfo?.type || 1,
        },
      },
    ],
    queryFn: async () =>
      await getViewMapReportFtthAccountList({
        province: modalInfo?.province?.code || '',
        status: (modalInfo?.status as EViewMapReportStatusFTTH) || 0,
        reportType: modalInfo?.type || 1,
      }),
    enabled: false,
  })

  useEffect(() => {
    if (modalInfo?.province?.code) {
      refetch()
    }
  }, [modalInfo?.province?.code, refetch])

  const dataReport = useMemo(() => {
    return data?.data?.ftths || []
  }, [data?.data?.ftths])

  const dataExport = useMemo(() => {
    return (
      dataReport.map((item, idx) => ({
        no: idx + 1,
        province: modalInfo?.province?.name ?? '',
        account: item.account,
        userUsing: item.userUsing,
        phone: item.phone,
        packages: item.packages,
        monthlyFee: `${item.monthlyFee}$`,
      })) || []
    )
  }, [dataReport, modalInfo?.province?.name])

  return (
    <Modal open={isOpen} onOpenChange={onCloseModal} contentClassName="!max-w-[838px]">
      <div className="w-full h-full flex flex-col items-center justify-center">
        <div className="py-4 flex justify-center items-center border-b-1 border-primary w-full">
          <p className="m-0 text-xl text-primary">List of FTTH accounts</p>
        </div>
        <div className="flex flex-col gap-2 mt-4 w-full">
          <p className="m-0 text-base">
            Province: <span className="text-primary">{modalInfo?.province?.name ?? ''}</span>
          </p>
          <p className="m-0 text-base">
            Status: <span className="text-primary">{STATUS_MAPPING[modalInfo?.status ?? 0]}</span>
          </p>
        </div>
        <div className="flex flex-col gap-2 mt-4 w-full">
          <div className="flex justify-between items-center w-full bg-neutral-200 py-3">
            <p className="m-0 text-base text-black w-[30%] text-center font-bold">No</p>
            <p className="m-0 text-base text-black w-[70%] text-center font-bold">Account</p>
            <p className="m-0 text-base text-black w-[70%] text-center font-bold">Customer name</p>
            <p className="m-0 text-base text-black w-[70%] text-center font-bold">Phone number</p>
            <p className="m-0 text-base text-black w-[70%] text-center font-bold">Package</p>
            <p className="m-0 text-base text-black w-[70%] text-center font-bold">Monthly fee</p>
          </div>
          <div className="overflow-y-auto max-h-[400px]">
            {isLoading ? (
              <div className="w-full flex justify-center items-center">
                <Spinner />
              </div>
            ) : (
              <>
                {dataReport.length > 0 ? (
                  dataReport.map((_, i) => (
                    <div key={i} className="flex justify-between items-center w-full py-3">
                      <p className="m-0 text-sm text-black w-[30%] text-center">{i + 1}</p>
                      <p className="m-0 text-sm text-black w-[70%] text-center">{_.account}</p>
                      <p className="m-0 text-sm text-black w-[70%] text-center">{_.userUsing}</p>
                      <p className="m-0 text-sm text-black w-[70%] text-center">{_.phone}</p>
                      <p className="m-0 text-sm text-black w-[70%] text-center">{_.packages}</p>
                      <p className="m-0 text-sm text-black w-[70%] text-center">{_.monthlyFee}$</p>
                    </div>
                  ))
                ) : (
                  <p className="m-0 text-sm text-center text-gray-400">No data found</p>
                )}
              </>
            )}
          </div>
          <p className="m-0 text-sm">
            Total: <span className="font-bold">{dataReport?.length}</span> account
            {dataReport?.length > 1 ? 's' : ''}
          </p>
        </div>
        <div className="w-full flex gap-2 justify-center items-center mt-4">
          <Button className="w-full" variant={ButtonVariant.NEUTRAL} onClick={onCloseModal}>
            Close
          </Button>
          <CustomerDataExporter
            className="w-full"
            disabled={!dataReport?.length || isLoading}
            columns={[
              { header: 'No', key: 'no', width: 15 },
              { header: 'Province', key: 'province', width: 15 },
              { header: 'Account', key: 'account', width: 20 },
              { header: 'Customer Name', key: 'userUsing', width: 15 },
              { header: 'Phone Number', key: 'phone', width: 15 },
              { header: 'Package', key: 'packages', width: 15 },
              { header: 'Monthly Fee', key: 'monthlyFee', width: 12 },
            ]}
            data={dataExport}
          />
        </div>
      </div>
    </Modal>
  )
}

export default ExportFTTHAccountModal
