import direction from '@/../public/images/direction.png'
import ggMap from '@/../public/images/gg-map.png'
import maptypeDefault from '@/../public/images/maptype-default.png'
import maptypeSatellite from '@/../public/images/maptype-satellite.png'
import { getListAccountFTTH, getListCableBox } from '@/api'
import Button, { ButtonVariant } from '@/components/Button'
import CustomerDataExporter from '@/components/ExportDataButton'
import Image from '@/components/Image'
import Modal from '@/components/Modal'
import {
  buildFilterCableBox,
  buildFilterFTTHAccountType,
  buildFilterFTTHStatus,
  DEFAULT_FILTER,
  getFTTHAccountIcon,
  getFTTHStatusColor,
  getFTTHStatusLabel,
  getStatusIcon,
} from '@/constants/view-map'
import { Route as ViewMapRoute } from '@/routes/view-map'
import useViewMapStore from '@/stores/viewMapStore'
import { IAccountFTTH } from '@/types'
import {
  formatDateToDDMMYYYY,
  getDistance,
  googleMapDirectionURL,
  googleMapLocationURL,
  pixelHeightToRadius,
} from '@/utils'
import { MarkerClusterer, Renderer, SuperClusterAlgorithm } from '@googlemaps/markerclusterer'
import { Circle, GoogleMap, InfoWindowF } from '@react-google-maps/api'
import { useQuery } from '@tanstack/react-query'
import { useSearch } from '@tanstack/react-router'
import clsx from 'clsx'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import type { FormFilterValues } from './components/Filter'
import LoadingSync from './components/LoadingSync'
import { MapControls } from './components/MapControls'
import SearchForm from './components/SearchForm'
import { useMapControl } from './hooks/useMapControl'
import { useMarkers } from './hooks/useMarkers'
import './index.css'

type Poi = {
  key: string
  location: google.maps.LatLngLiteral
  status: number
  type?: number
  ftthInfo?: IAccountFTTH
}

type MapBoundsDetails = {
  minLat?: number
  maxLat?: number
  minLng?: number
  maxLng?: number
}

const CHUNK_SIZE = 5000

const clusterRenderer: Renderer = {
  render: ({ count, position }) => {
    try {
      let text = count.toString()
      let backgroundColor = '#1e90ff' // Xanh lam (#1e90ff), đỏ (#ff0000)

      if (count > 10) {
        text = '10+'
        backgroundColor = '#FFFF00'
      }

      if (count > 50) {
        text = '50+'
        backgroundColor = '#ff0000'
      }

      if (count > 100) {
        text = '100+'
        backgroundColor = '#FF69B4'
      }

      const size = Math.min(40 + Math.log(count) * 10, 60) // Giới hạn tối đa 60px
      // Màu nền: xanh lam cho cụm nhỏ, đỏ cho cụm lớn (>50)

      // Tạo SVG cho icon cụm
      const svg = `
            <svg width="${size}" height="${size}" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="50" r="40" fill="${backgroundColor}" opacity="0.5"/>
              <circle cx="50" cy="50" r="35" fill="${backgroundColor}" opacity="0.6"/>
              <circle cx="50" cy="50" r="30" fill="${backgroundColor}" opacity="0.7"/>
              <circle cx="50" cy="50" r="25" fill="${backgroundColor}" opacity="0.9"/>
            </svg>
        `

      return new window.google.maps.Marker({
        position,
        icon: {
          url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svg)}`,
          scaledSize: new window.google.maps.Size(size, size),
        },
        zIndex: Number(window.google.maps.Marker.MAX_ZINDEX) + count,
        label: {
          text,
          color: 'black',
          fontSize: `${12}px`,
        },
      })
    } catch (error) {
      console.error('Renderer error:', error)
      // Fallback: trả về marker mặc định nếu renderer thất bại
      return new window.google.maps.Marker({
        position,
        label: { text: String(count), color: 'white', fontSize: '14px' },
      })
    }
  },
}

const ViewMap = memo(() => {
  const { currentLocation } = useViewMapStore((state) => state)
  const search = useSearch({ from: ViewMapRoute.fullPath })
  const circleRef = useRef<Circle | null>(null)

  const {
    mapRef,
    mapType,
    mapBounds,
    circleCenter,
    setCircleCenter,
    handleIdle,
    switchType,
    CAMBODIA_CENTER,
  } = useMapControl()

  const [mapBoundsDetails, setMapBoundsDetails] = useState<MapBoundsDetails>()
  const [lastSignificantBounds, setLastSignificantBounds] = useState<MapBoundsDetails>()
  const [lastZoomLevel, setLastZoomLevel] = useState<number>(8)
  const [cableBoxPoints, setCableBoxPoints] = useState<Poi[]>([])
  const [ftthAccountPoints, setFtthAccountPoints] = useState<Poi[]>([])
  const [loading, setLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  // const [radiusToPixel, setRadiusToPixel] = useState(0)
  const [radius, setRadius] = useState(1500)
  const [isCheck, setIsCheck] = useState(false)
  const [showOptions, setShowOptions] = useState(false)

  const [loadingPercent, setLoadingPercent] = useState(0)

  const [formFilter, setFormFilter] = useState<FormFilterValues>(DEFAULT_FILTER)

  const [positionOffset, setPositionOffset] = useState(0)

  const [lastCenter, setLastCenter] = useState<google.maps.LatLngLiteral>(circleCenter)

  const [maxRadius, setMaxRadius] = useState(radius * 2)

  const [clusterer, setClusterer] = useState<MarkerClusterer | null>(null)

  const searchParams: { province: string; lat?: number; long?: number } = useMemo(() => {
    return {
      province: (search as { province?: string }).province || '',
      lat: (search as { lat?: number }).lat || 0,
      long: (search as { long?: number }).long || 0,
    }
  }, [search])

  const isSignificantChange = (newBounds: MapBoundsDetails, newZoom: number) => {
    if (!lastSignificantBounds || lastZoomLevel === undefined) return true

    // Kiểm tra thay đổi zoom (ít nhất 1 level)
    if (Math.abs(newZoom - lastZoomLevel) >= 1) return true

    // Kiểm tra thay đổi vị trí (ít nhất 0.01 độ)
    return (
      Math.abs((newBounds.minLat || 0) - (lastSignificantBounds.minLat || 0)) > 0.01 ||
      Math.abs((newBounds.minLng || 0) - (lastSignificantBounds.minLng || 0)) > 0.01 ||
      Math.abs((newBounds.maxLat || 0) - (lastSignificantBounds.maxLat || 0)) > 0.01 ||
      Math.abs((newBounds.maxLng || 0) - (lastSignificantBounds.maxLng || 0)) > 0.01
    )
  }

  const {
    refetch: getCableBoxes,
    isLoading: isLoadingCableBox,
    data: listCableBoxData,
  } = useQuery({
    queryKey: ['cableBox', { params: { province: '' } }],
    queryFn: async () =>
      await getListCableBox({
        province: searchParams.province,
        zoomLevel: mapRef?.current?.getZoom(),
        maxLat: mapBoundsDetails?.maxLat,
        minLat: mapBoundsDetails?.minLat,
        maxLng: mapBoundsDetails?.maxLng,
        minLng: mapBoundsDetails?.minLng,
      }),
    enabled: false,
  })

  const {
    visibleCableBoxLocations,
    visibleFtthAccountLocations,
    selectedCableMarker,
    setSelectedCableMarker,
    selectedFtthMarker,
    setSelectedFtthMarker,
    setVisibleCableBoxLocations,
    setVisibleFtthAccountLocations,
  } = useMarkers(mapBounds, cableBoxPoints, ftthAccountPoints, formFilter)

  const handleCloseInfo = useCallback(() => {
    setSelectedCableMarker(null)
    setSelectedFtthMarker(null)
  }, [setSelectedCableMarker, setSelectedFtthMarker])

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const loadFTTHAccounts = useCallback(
    async (zoomLevel?: number) => {
      try {
        setLoading(true)
        const promises = [...Array.from({ length: 10 })].map((_, index) =>
          getListAccountFTTH({
            province: searchParams.province,
            number: index,
            zoomLevel,
            maxLat: mapBoundsDetails?.maxLat,
            minLat: mapBoundsDetails?.minLat,
            maxLng: mapBoundsDetails?.maxLng,
            minLng: mapBoundsDetails?.minLng,
          })
        )
        const results = await Promise.allSettled(promises)
        return results
          .filter((result) => result.status === 'fulfilled')
          .map((result) => result.value.data?.accountFTTHs || [])
          .flat()
      } catch (error) {
        console.error('Error loading data:', error)
        //
      }
    },
    [
      mapBoundsDetails?.maxLat,
      mapBoundsDetails?.maxLng,
      mapBoundsDetails?.minLat,
      mapBoundsDetails?.minLng,
      searchParams.province,
    ]
  )

  const handleLoadChunkData = useCallback(
    async (zoomLevel?: number) => {
      try {
        // 1. Fetch data
        setLoading(true)
        const results = await Promise.allSettled([getCableBoxes()])

        const [cableBoxResult] = results

        const ftths = await loadFTTHAccounts(zoomLevel)
        const listCableBoxData = cableBoxResult.status === 'fulfilled' ? cableBoxResult.value : null

        const listCableBox = listCableBoxData?.data?.data?.cableBoxs || []

        const listFTTH = ftths || []

        // 2. Prepare data
        const cableBoxData = listCableBox.map((item) => ({
          key: item.cableBox,
          location: { lat: +item.lat, lng: +item.lng },
          status: item.status,
        }))

        const ftthData = listFTTH.map((item) => ({
          key: item.account,
          location: { lat: +item.custLat, lng: +item.custLong },
          status: item.statusFtth,
          type: item.mobileAccount,
          ftthInfo: item,
        }))

        // 3. Initialize loading
        setCableBoxPoints([])
        setFtthAccountPoints([])
        setLoadingPercent(0)

        // 4. Track processed keys outside React lifecycle
        const processedKeys = new Set()
        let cableIndex = 0
        let ftthIndex = 0

        // 5. Chunk processor
        const processChunk = () => {
          // Prepare chunks
          const cableChunk: Poi[] = []

          const ftthChunk: Poi[] = []

          // Process cable boxes
          while (cableIndex < cableBoxData.length && cableChunk.length < CHUNK_SIZE) {
            const item = cableBoxData[cableIndex]
            if (!processedKeys.has(item.key)) {
              cableChunk.push(item)
              processedKeys.add(item.key)
            }
            cableIndex++
          }

          // Process FTTH
          while (ftthIndex < ftthData.length && ftthChunk.length < CHUNK_SIZE) {
            const item = ftthData[ftthIndex]
            if (!processedKeys.has(item.key)) {
              ftthChunk.push(item)
              processedKeys.add(item.key)
            }
            ftthIndex++
          }

          // 6. Batch update with functional updates
          setCableBoxPoints((prev) => {
            const existingKeys = new Set(prev.map((i) => i.key))
            const filtered = cableChunk.filter((item) => !existingKeys.has(item.key))
            return filtered.length ? [...prev, ...filtered] : prev
          })

          setFtthAccountPoints((prev) => {
            const existingKeys = new Set(prev.map((i) => i.key))
            const filtered = ftthChunk.filter((item) => !existingKeys.has(item.key))
            return filtered.length ? [...prev, ...filtered] : prev
          })

          // 7. Update progress
          const total = cableBoxData.length + ftthData.length
          const processed = cableIndex + ftthIndex
          setLoadingPercent(
            Math.min(Math.round((isNaN(processed / total) ? 0 : processed / total) * 100), 100)
          )

          // 8. Continue or finish
          if (cableIndex < cableBoxData.length || ftthIndex < ftthData.length) {
            setTimeout(processChunk, 0) // Giải phóng event loop
          } else {
            setLoading(false)
            setLoadingPercent(0)
          }
        }

        processChunk()
      } catch (error) {
        console.error('Load data failed:', error)
        setLoading(false)
      }
    },
    [getCableBoxes, loadFTTHAccounts]
  )

  useEffect(() => {
    if (searchParams.province) {
      const zoomLevel = mapRef.current?.getZoom()
      console.log('🚀 ~ useEffect ~ zoomLevel:', zoomLevel)
      handleLoadChunkData(zoomLevel)
    }
    handleCloseInfo()
  }, [handleCloseInfo, handleLoadChunkData, mapRef.current?.getZoom(), searchParams.province])

  const handleMarkerClick = useCallback(
    (marker: Poi) => {
      const cableBox = listCableBoxData?.data.cableBoxs?.find(
        (item) => item.cableBox === marker.key
      )
      setSelectedCableMarker(cableBox || null)
    },
    [listCableBoxData, setSelectedCableMarker]
  )

  const handleMarkerClickFTTH = useCallback(
    (marker: Poi) => {
      const ftthAccount = ftthAccountPoints?.find((item) => item.ftthInfo?.account === marker.key)
      if (!ftthAccount?.ftthInfo) return
      setSelectedFtthMarker(ftthAccount.ftthInfo)
    },
    [setSelectedFtthMarker, ftthAccountPoints]
  )

  useEffect(() => {
    if (clusterer && mapRef.current) {
      // const zoom = mapRef.current.getZoom() || 0;
      // const shouldCluster = zoom <= 14;
      // Xóa các marker cũ
      clusterer.clearMarkers()

      // Tạo danh sách marker mới
      const visibleCableBoxLocationsMarkers = visibleCableBoxLocations.map((data) => {
        const marker = new window.google.maps.Marker({
          position: data.location,
          icon: {
            url: getStatusIcon(data.status),
            scaledSize: new window.google.maps.Size(24, 24),
          },
          clickable: true,
        })
        marker.addListener('click', () => handleMarkerClick(data))
        return marker
      })

      const visibleFtthAccountLocationsMarkers = visibleFtthAccountLocations.map((data) => {
        const marker = new window.google.maps.Marker({
          position: data.location,
          icon: {
            url: getFTTHAccountIcon(data.type || 0, getFTTHStatusColor(data.status)),
            scaledSize: new window.google.maps.Size(16, 16),
          },
          clickable: true,
        })
        marker.addListener('click', () => handleMarkerClickFTTH(data))
        return marker
      })
      // Thêm marker vào clusterer
      clusterer.addMarkers(visibleCableBoxLocationsMarkers)
      clusterer.addMarkers(visibleFtthAccountLocationsMarkers)
    }
  }, [
    clusterer,
    handleMarkerClick,
    handleMarkerClickFTTH,
    visibleCableBoxLocations,
    visibleFtthAccountLocations,
    mapRef,
  ])

  const nearbyLocations = useMemo(() => {
    return ftthAccountPoints
      ?.map((item) => item.ftthInfo)
      .filter(
        (loc) =>
          getDistance(
            lastCenter?.lat,
            lastCenter?.lng,
            Number(loc?.custLat),
            Number(loc?.custLong)
          ) <= radius
      )
  }, [ftthAccountPoints, lastCenter?.lat, lastCenter?.lng, radius])

  const handleClickDirection = useCallback(async () => {
    if (!currentLocation) {
      alert('Something went wrong when getting your location. Please try again.')
      return
    }

    if (!selectedFtthMarker) {
      return
    }
    window.open(
      googleMapDirectionURL({
        currentLocation: {
          lat: currentLocation.latitude,
          lng: currentLocation.longitude,
        },
        targetLocation: {
          lat: Number(selectedFtthMarker.custLat),
          lng: Number(selectedFtthMarker.custLong),
        },
      }),
      '_blank'
    )
  }, [currentLocation, selectedFtthMarker])

  const handleClickLocation = useCallback(() => {
    if (!selectedFtthMarker) {
      return
    }
    window.open(
      googleMapLocationURL({
        lat: Number(selectedFtthMarker.custLat),
        lng: Number(selectedFtthMarker.custLong),
      }),
      '_blank'
    )
  }, [selectedFtthMarker])

  const handleOnSubmitSearchForm = ({ lat, long }: { lat?: number; long?: number }) => {
    setCircleCenter({
      lat: lat || CAMBODIA_CENTER.lat,
      lng: long || CAMBODIA_CENTER.lng,
    })
    mapRef.current?.setZoom(17)
  }

  const onFilter = useCallback(
    (dataFilter: FormFilterValues): number => {
      setFormFilter(dataFilter)
      const isAppliedFilter = Object.values(dataFilter).some((value) => Boolean(value))
      const cableBoxFilters = buildFilterCableBox(
        {
          less_3_m: dataFilter.less_3_m,
          less_6_m: dataFilter.less_6_m,
          less_12_m: dataFilter.less_12_m,
          over_12_m: dataFilter.over_12_m,
        },
        isAppliedFilter
      )

      const ftthAccountStatusFilters = buildFilterFTTHStatus(
        {
          block_1_m: dataFilter.block_1_m,
          block_2_m: dataFilter.block_2_m,
          block_3_m: dataFilter.block_3_m,
          terminate_block_over_3_m: dataFilter.terminate_block_over_3_m,
          new_active: dataFilter.new_active,
        },
        isAppliedFilter
      )

      const ftthAccountTypeFilters = buildFilterFTTHAccountType(
        {
          '0_account': dataFilter['0_account'],
          '1_account': dataFilter['1_account'],
          '2_account': dataFilter['2_account'],
        },
        isAppliedFilter
      )

      const newCableBoxs = cableBoxPoints.filter((item) => cableBoxFilters.includes(item.status))
      const newFtthAccount = ftthAccountPoints.filter((item) => {
        return (
          ftthAccountStatusFilters.includes(item.status) &&
          ftthAccountTypeFilters.includes(item.type || 0)
        )
      })

      if (newCableBoxs.length === 0 && newFtthAccount.length === 0) {
        return 1
      } else {
        setVisibleCableBoxLocations(newCableBoxs)
        setVisibleFtthAccountLocations(newFtthAccount)
        return 0
      }
    },
    [cableBoxPoints, ftthAccountPoints, setVisibleCableBoxLocations, setVisibleFtthAccountLocations]
  )

  const handleOpenCircle = () => {
    setIsCheck(true)
    if (mapRef.current) {
      const aQuarterHeight = mapRef.current.getDiv().clientHeight / 4
      const center = mapRef.current.getCenter()
      if (!center) return
      const radiusWithRatio = pixelHeightToRadius(
        aQuarterHeight,
        center.lat(),
        mapRef.current.getZoom() || 0
      )
      setRadius(radiusWithRatio)
      setLastCenter({
        lat: center.lat(),
        lng: center.lng(),
      })
      setMaxRadius(radiusWithRatio * 2)
    }
  }

  const handleDragEndCircle = useCallback((e: google.maps.MapMouseEvent) => {
    if (!e.latLng) return
    const newPos = {
      lat: circleRef.current?.state.circle?.getCenter()?.lat() || 0,
      lng: circleRef.current?.state.circle?.getCenter()?.lng() || 0,
    }
    setLastCenter(newPos)
  }, [])

  const handleOpenExportDataModal = () => {
    if (!nearbyLocations.length) {
      setIsCheck(false)
      return
    }
    setIsOpen(true)
  }

  const handleZoomToProvince = useCallback(
    ({ latitude, longitude }: { latitude: number; longitude: number }) => {
      if (!mapRef.current) return
      mapRef.current.setCenter({
        lat: latitude,
        lng: longitude,
      })
      mapRef.current.setZoom(10)
    },
    [mapRef]
  )

  return (
    <>
      <GoogleMap
        onIdle={() => handleIdle(mapRef.current)}
        mapContainerStyle={{ height: '100%', width: '100%', pointerEvents: 'auto' }}
        center={circleCenter}
        zoom={8}
        options={{
          disableDefaultUI: true,
          zoomControl: true,
          zoomControlOptions: {
            position: window.google?.maps.ControlPosition.RIGHT_BOTTOM,
          },
          streetViewControl: false,
          fullscreenControl: false,
          mapTypeControl: false,
        }}
        onLoad={(map) => {
          if (!map) return
          map.setMapTypeId(mapType)
          mapRef.current = map
          const center = map.getCenter()
          if (!center) return
          setCircleCenter({ lat: center.lat(), lng: center.lng() })

          const clustererInstance = new MarkerClusterer({
            map: map,
            markers: [],
            renderer: clusterRenderer,
            algorithm: new SuperClusterAlgorithm({ minPoints: 5, radius: 60 }),
            onClusterClick: (cluster) => {
              setCircleCenter({
                lat: cluster.latLng?.lat() || 0,
                lng: cluster.latLng?.lng() || 0,
              })
              mapRef.current?.setZoom(15)
              handleCloseInfo()
            },
          })
          setClusterer(clustererInstance)
          const initialBounds = {
            minLat: map.getBounds()?.getSouthWest().lat(),
            maxLat: map.getBounds()?.getNorthEast().lat(),
            minLng: map.getBounds()?.getSouthWest().lng(),
            maxLng: map.getBounds()?.getNorthEast().lng(),
          }

          setMapBoundsDetails(initialBounds)
          setLastSignificantBounds(initialBounds)
          setLastZoomLevel(map.getZoom() || 8)
        }}
        onDragEnd={() => {
          if (mapRef.current) {
            const newCenter = mapRef.current.getCenter()
            if (newCenter) {
              setCircleCenter({ lat: newCenter.lat(), lng: newCenter.lng() })
            }
            const newBounds = {
              minLat: mapRef.current.getBounds()?.getSouthWest().lat(),
              maxLat: mapRef.current.getBounds()?.getNorthEast().lat(),
              minLng: mapRef.current.getBounds()?.getSouthWest().lng(),
              maxLng: mapRef.current.getBounds()?.getNorthEast().lng(),
            }
            const currentZoom = mapRef.current.getZoom() || 8

            if (isSignificantChange(newBounds, currentZoom)) {
              setMapBoundsDetails(newBounds)
              setLastSignificantBounds(newBounds)
              setLastZoomLevel(currentZoom)
            }
          }
        }}
        onZoomChanged={() => {
          if (mapRef.current) {
            // get 1/3 height of map (1/3 screen height)
            const halfHeight = mapRef.current.getDiv().clientHeight / 3
            const center = mapRef.current.getCenter()

            const currentZoom = mapRef.current.getZoom() || 8
            const newBounds = {
              minLat: mapRef.current.getBounds()?.getSouthWest().lat(),
              maxLat: mapRef.current.getBounds()?.getNorthEast().lat(),
              minLng: mapRef.current.getBounds()?.getSouthWest().lng(),
              maxLng: mapRef.current.getBounds()?.getNorthEast().lng(),
            }
            if (!center) return
            const radiusWithRatio = pixelHeightToRadius(
              halfHeight,
              center.lat(),
              mapRef.current.getZoom() || 0
            )
            setMaxRadius(radiusWithRatio)

            const calculateOffset = () => {
              const baseOffset = 0.009 // Base offset at zoom level 10
              const zoomFactor = Math.pow(2, 12 - (mapRef.current?.getZoom() ?? 0)) // Adjust 12 to change sensitivity
              return baseOffset * zoomFactor
            }
            setPositionOffset(calculateOffset())
            // Chỉ cập nhật bounds nếu có thay đổi đáng kể
            if (isSignificantChange(newBounds, currentZoom)) {
              setMapBoundsDetails(newBounds)
              setLastSignificantBounds(newBounds)
              setLastZoomLevel(currentZoom)
            }
          }
        }}
        onClick={(e) => {
          console.log('Clicked map' + e)
          handleCloseInfo()
        }}
      >
        <Circle
          ref={circleRef}
          visible={isCheck}
          center={lastCenter || circleCenter}
          radius={radius}
          options={{ fillColor: 'transparent', strokeColor: '#169287' }}
          onDragEnd={handleDragEndCircle}
          draggable
        />

        {selectedCableMarker && (
          <InfoWindowF
            position={{
              lat: isNaN(Number(selectedCableMarker.lat))
                ? 0
                : Number(selectedCableMarker.lat) + positionOffset,
              lng: isNaN(Number(selectedCableMarker.lng)) ? 0 : Number(selectedCableMarker.lng),
            }}
            options={{
              headerDisabled: true,
            }}
            onCloseClick={handleCloseInfo}
          >
            <div className="w-full max-w-[200px]">
              <p className="m-0">
                <span className="text-sm font-semibold">Cable box: </span>
                {selectedCableMarker.cableBox}
              </p>
              <p className="m-0">
                <span className="text-sm font-semibold">Create date: </span>
                {formatDateToDDMMYYYY(selectedCableMarker.createDate)}
              </p>
              <p className="m-0">
                <span className="text-sm font-semibold">Usage Volume: </span>
                {selectedCableMarker.totalCount}/{selectedCableMarker.target} (
                {((selectedCableMarker.totalCount / selectedCableMarker.target) * 100).toFixed(2)}%)
              </p>
            </div>
          </InfoWindowF>
        )}
        {selectedFtthMarker && (
          <InfoWindowF
            position={{
              lat: isNaN(Number(selectedFtthMarker.custLat))
                ? 0
                : Number(selectedFtthMarker.custLat) + positionOffset,
              lng: isNaN(Number(selectedFtthMarker.custLong))
                ? 0
                : Number(selectedFtthMarker.custLong),
            }}
            options={{
              headerDisabled: true,
            }}
            onCloseClick={handleCloseInfo}
          >
            <div className="w-full max-w-[200px]">
              <p className="m-0">
                <span className="text-sm font-semibold">Account: </span>
                {selectedFtthMarker.account}
              </p>
              <p className="m-0">
                <span className="text-sm font-semibold">Customer name: </span>
                {selectedFtthMarker.userUsing}
              </p>
              <p className="m-0">
                <span className="text-sm font-semibold">Phone number: </span>
                {selectedFtthMarker.phone}
              </p>
              <p className="m-0">
                <span className="text-sm font-semibold">Package: </span>
                {selectedFtthMarker.packages}
              </p>
              <p className="m-0">
                <span className="text-sm font-semibold">Monthly fee: </span>
                {selectedFtthMarker.monthlyFee} $
              </p>
            </div>
          </InfoWindowF>
        )}
      </GoogleMap>
      {selectedFtthMarker ? (
        <div
          className="absolute bottom-[25px] right-[5px] z-10 flex gap-2 items-end bg-white rounded-xl shadow-lg"
          onMouseEnter={() => setShowOptions(true)}
          onMouseLeave={() => setShowOptions(false)}
        >
          <div className="w-15 h-15 cursor-pointer p-2" onClick={handleClickDirection}>
            <Image src={direction} />
          </div>
          <div
            className="w-15 h-15 cursor-pointer p-2 shadow-md rounded-lg"
            onClick={handleClickLocation}
          >
            <Image src={ggMap} />
          </div>
        </div>
      ) : (
        <div
          className="absolute bottom-[15px] right-[5px] z-10 flex gap-2 items-end"
          onMouseEnter={() => setShowOptions(true)}
          onMouseLeave={() => setShowOptions(false)}
        >
          {showOptions && (
            <div className="mt-2 shadow-md rounded flex gap-2 bg-white p-2 pb-5">
              <div
                className={clsx(
                  'w-[60px] h-[60px] cursor-pointer border border-white hover:border-primary rounded-lg hover:text-primary',
                  {
                    '!border-primary': mapType === 'roadmap',
                  }
                )}
                onClick={() => switchType('roadmap' as google.maps.MapTypeId)}
              >
                <Image src={maptypeDefault} />
                <p
                  className={clsx('text-center text-xs', {
                    '!text-primary': mapType === 'roadmap',
                  })}
                >
                  Default
                </p>
              </div>
              <div
                className={clsx(
                  'w-[60px] h-[60px] cursor-pointer border border-white hover:border-primary rounded-lg hover:text-primary',
                  {
                    '!border-primary': mapType === 'satellite',
                  }
                )}
                onClick={() => switchType('satellite' as google.maps.MapTypeId)}
              >
                <Image src={maptypeSatellite} />
                <p
                  className={clsx('text-center text-xs', {
                    '!text-primary': mapType === 'satellite',
                  })}
                >
                  Satellite
                </p>
              </div>
            </div>
          )}
          <div className="w-[80px] h-[80px] cursor-pointer">
            <Image key={mapType} src={mapType === 'roadmap' ? maptypeDefault : maptypeSatellite} />
          </div>
        </div>
      )}

      <MapControls
        isCheck={isCheck}
        setIsCheck={setIsCheck}
        setIsOpen={setIsOpen}
        handleLoadChunkData={handleLoadChunkData}
        onFilter={onFilter}
        radius={radius}
        setRadius={setRadius}
        onOpenCircle={handleOpenCircle}
        maxRadius={maxRadius}
        onOpenExportDataModel={handleOpenExportDataModal}
      />
      <div className="absolute top-25 left-5">
        <SearchForm
          onSearch={handleOnSubmitSearchForm}
          handleZoomToProvince={handleZoomToProvince}
        />
      </div>
      {(isLoadingCableBox || loading) && <LoadingSync percentage={loadingPercent} />}
      <Modal open={isOpen} onOpenChange={setIsOpen}>
        <div className="w-full h-full flex flex-col items-center justify-center">
          <div className="py-4 flex justify-center items-center border-b-1 border-primary w-full">
            <p className="m-0 text-xl text-primary">List of FTTH accounts</p>
          </div>
          <div className="flex flex-col gap-2 mt-4 w-full">
            <div className="flex justify-between items-center w-full bg-neutral-200 py-3">
              <p className="m-0 text-base text-black w-[30%] text-center font-bold">No</p>
              <p className="m-0 text-base text-black w-[70%] text-center font-bold">Account</p>
            </div>
            <div className="overflow-y-auto max-h-[400px]">
              {nearbyLocations?.map((_, i) => (
                <div key={i} className="flex justify-between items-center w-full py-3">
                  <p className="m-0 text-sm text-black w-[30%] text-center">{i + 1}</p>
                  <p className="m-0 text-sm text-black w-[70%] text-center">
                    {_?.account ?? '----'}
                  </p>
                </div>
              ))}
            </div>
            <p className="m-0 text-sm">
              Total: <span className="font-bold">{nearbyLocations?.length}</span> account
              {`${nearbyLocations?.length > 1 ? 's' : ''}`}
            </p>
          </div>
          <div className="w-full flex gap-2 justify-center items-center mt-4">
            <Button
              className="w-full"
              variant={ButtonVariant.NEUTRAL}
              onClick={() => setIsOpen(false)}
            >
              Close
            </Button>
            <CustomerDataExporter
              className="w-full"
              disabled={!nearbyLocations?.length}
              columns={[
                { header: 'Account', key: 'account', width: 15 },
                { header: 'Customer Name', key: 'userUsing', width: 20 },
                { header: 'Status', key: 'status', width: 10 },
                { header: 'Phone Number', key: 'phone', width: 15 },
                { header: 'Package', key: 'packages', width: 15 },
                { header: 'Monthly Fee', key: 'monthlyFee', width: 12 },
              ]}
              data={(nearbyLocations as IAccountFTTH[])?.map((item) => ({
                ...item,
                status: getFTTHStatusLabel(item.statusFtth),
                monthlyFee: `${item.monthlyFee} $`,
              }))}
            />
          </div>
        </div>
      </Modal>
    </>
  )
})

export default ViewMap
