export interface IProvince {
  code: string
  name: string
}

export interface IProvinceResponse {
  errorCode?: string
  errorMessage?: string
  lstProvince: IProvince[]
}

export interface ICableBoxRequestParams {
  province: string
  page?: number
  pageSize?: number
  zoomLevel?: number
  minLat?: number
  maxLat?: number
  minLng?: number
  maxLng?: number
}

export interface ICableBox {
  cableBox: string
  createDate: string
  totalCount: number
  target: number
  status: number
  lat: string
  lng: string
}

export interface ICableBoxResponse {
  cableBoxs: ICableBox[]
}

export interface IAccountFTTHRequestParams {
  province: string
  number: number
  zoomLevel?: number
  minLat?: number
  maxLat?: number
  minLng?: number
  maxLng?: number
}

export interface IAccountFTTH {
  account: string
  userUsing: string
  phone: string
  statusFtth: number
  packages: string
  monthlyFee: number
  custLat: string
  custLong: string
  mobileAccount: number
}

export interface IAccountFTTHResponse {
  accountFTTHs: IAccountFTTH[]
}

export interface IViewMapReportParams {
  province?: string
  page: number
  pageSize?: number
}

export interface IViewMapReport {
  province: string
  reportCablebox: {
    gt12Months: number
    lt12Months: number
    lt3Months: number
    lt6Months: number
  }
  reportFtth: {
    activeThisMonth: number
    block1Month: number
    block2Month: number
    block3Month: number
    blockOver3Month: number
  }
}

export interface IViewMapReportResponse {
  reports: IViewMapReport[]
  totalPages: number
  totalRecords: number
}

export enum EViewMapReportType {
  CABLE_BOX = 0,
  FTTH_ACCOUNT = 1,
}

export enum EViewMapReportCableBoxStatus {
  LESS_3_MONTHS = 0,
  LESS_6_MONTHS = 1,
  LESS_12_MONTHS = 2,
  OVER_12_MONTHS = 3,
}

export enum EViewMapReportStatusFTTH {
  NEW_ACTIVE = 0,
  BLOCK_1_MONTH = 1,
  BLOCK_2_MONTHS = 2,
  BLOCK_3_MONTHS = 3,
  TERMINATE_BLOCK_OVER_3_MONTHS = 4,
}

export type IViewMapReportCableboxParams = {
  province: string
  page?: number
  pageSize?: number
  status: EViewMapReportCableBoxStatus
  reportType: EViewMapReportType
}

export type IViewMapReportFtthAccountParams = {
  province: string
  page?: number
  pageSize?: number
  status: EViewMapReportStatusFTTH
  reportType: EViewMapReportType
}
export interface IViewMapReportCableboxResponse {
  cableboxs?: ICableBox[]
}

export interface IViewMapReportFtthAccountDetailResponse {
  ftths?: IAccountFTTH[]
}
