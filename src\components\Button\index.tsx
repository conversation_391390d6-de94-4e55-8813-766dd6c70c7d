import clsx from 'clsx'
import React, { forwardRef } from 'react'
import Spinner from '../Spinner'

export enum ButtonVariant {
  OUTLINE_NEUTRAL = 'outline-neutral',
  OUTLINE_PRIMARY = 'outline-primary',
  PRIMARY = 'primary',
  HIGHLIGHT = 'highlight',
  DANGER = 'danger',
  WARNING = 'warning',
  SUCCESS = 'success',
  INFO = 'info',
  NEUTRAL = 'neutral',
}

export enum ButtonSize {
  SM = 'sm',
  MD = 'md',
  LG = 'lg',
}

export type Rounded = 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'circle'

export type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  variant?: ButtonVariant
  size?: ButtonSize
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  rounded?: Rounded
  shadow?: boolean
}

const variantClasses: Record<ButtonVariant, string> = {
  [ButtonVariant.OUTLINE_PRIMARY]:
    'border border-primary text-primary bg-white hover:bg-primary-light',
  [ButtonVariant.OUTLINE_NEUTRAL]: 'text-primary bg-white hover:bg-primary-light',
  [ButtonVariant.NEUTRAL]: 'text-btn-neutral-text bg-btn-neutral-bg hover:bg-btn-neutral-bg-hover',
  [ButtonVariant.PRIMARY]: 'bg-primary text-white hover:bg-primary/90',
  [ButtonVariant.HIGHLIGHT]: 'bg-indigo-500 text-white hover:bg-indigo-600',
  [ButtonVariant.DANGER]: 'bg-red-600 text-white hover:bg-red-700',
  [ButtonVariant.WARNING]: 'bg-yellow-500 text-white hover:bg-yellow-600',
  [ButtonVariant.SUCCESS]: 'bg-green-600 text-white hover:bg-green-700',
  [ButtonVariant.INFO]: 'bg-cyan-500 text-white hover:bg-cyan-600',
}

const sizeClasses: Record<ButtonSize, string> = {
  [ButtonSize.SM]: 'text-sm px-3 py-1.5',
  [ButtonSize.MD]: 'text-base px-4 py-3',
  [ButtonSize.LG]: 'text-lg px-5 py-3',
}

const roundedClasses: Record<Rounded, string> = {
  none: 'rounded-none',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  '2xl': 'rounded-2xl',
  full: 'rounded-full',
  circle: 'rounded-full !p-2',
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(function Button(
  {
    children,
    variant = ButtonVariant.PRIMARY,
    size = ButtonSize.SM,
    className = '',
    loading = false,
    disabled = false,
    icon,
    iconPosition = 'left',
    rounded = 'md',
    shadow = false,
    ...props
  },
  ref
) {
  const isDisabled = loading || disabled

  return (
    <button
      ref={ref}
      disabled={isDisabled}
      className={clsx(
        'rounded font-medium transition-all focus:outline-none inline-flex items-center justify-center gap-2 cursor-pointer min-h-[36px]',
        variantClasses[variant],
        sizeClasses[size],
        roundedClasses[rounded],
        isDisabled && 'opacity-60 !cursor-not-allowed',
        shadow && 'shadow-md',
        className
      )}
      {...props}
    >
      {loading ? (
        <Spinner />
      ) : (
        <>
          {icon && iconPosition === 'left' && <span>{icon}</span>}
          <span className="text-base font-normal">{children}</span>
          {icon && iconPosition === 'right' && <span>{icon}</span>}
        </>
      )}
    </button>
  )
})

export default Button
