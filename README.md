# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

## Installation

Run dev mode

```bash
npm run dev
```

Build

```bash
npm run build
```

## Tech Stack

[TanStack Router](https://tanstack.com/router/latest)

[TanStack Query](https://tanstack.com/query/latest)

[Zustand](https://zustand.docs.pmnd.rs/getting-started/introduction)

[TailwindCSS](https://tailwindcss.com/)

## License

[MIT](https://choosealicense.com/licenses/mit/)
