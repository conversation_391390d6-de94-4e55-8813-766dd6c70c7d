import { TOKEN, USER } from '@/constants'
import useAuthStore from '@/stores/authStore'
import { ApiError } from '@/types'
import axios, { AxiosError, AxiosResponse } from 'axios'

const BASE_API_URL = import.meta.env.VITE_BASE_API_URL || 'http://localhost:8080/api'

const axiosInstance = axios.create({
  baseURL: BASE_API_URL,
  headers: {
    'Content-Type': 'application/json',
    locale: 'en_US',
  },
})

axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(TOKEN)
    const user = localStorage.getItem(USER)
    if (token) {
      config.headers.Authorization = `${token}`
    }
    if (user) {
      const parsedUser = JSON.parse(user)
      config.headers['staff-code'] = `${(parsedUser?.username || '').toUpperCase()}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    if (response.data.errorCode === '401') {
      useAuthStore.getState().checkTokenExpiration()
    }
    return response
  },
  (error: AxiosError<ApiError>) => {
    return Promise.reject(error)
  }
)

export default axiosInstance

// Custom error handler with type checking
export class ApiErrorHandler extends Error {
  constructor(
    public status: number,
    public message: string,
    public errors?: Record<string, string[]>
  ) {
    super(message)
    Object.setPrototypeOf(this, ApiErrorHandler.prototype)
  }

  static fromAxiosError(error: AxiosError<ApiError>): ApiErrorHandler {
    const status = error.response?.status || 500
    const message = error.response?.data?.message || error.message
    const errors = error.response?.data?.errors

    return new ApiErrorHandler(status, message, errors)
  }
}

// Type guard for checking if an error is an AxiosError
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isAxiosError<T = any>(error: unknown): error is AxiosError<T> {
  return axios.isAxiosError(error)
}
