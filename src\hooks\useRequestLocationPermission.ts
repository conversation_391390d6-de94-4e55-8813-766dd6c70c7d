import useViewMapStore from '@/stores/viewMapStore'
import { useCallback, useEffect } from 'react'

const useRequestLocationPermission = () => {
  const { setCurrentLocation } = useViewMapStore()

  const getLocation = useCallback(() => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords
        setCurrentLocation({ latitude, longitude })
      },
      (error) => {
        console.error('Error getting location:', error)
      }
    )
  }, [setCurrentLocation])

  const requestLocationPermission = useCallback(async () => {
    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' })

      if (permission.state === 'granted') {
        console.log('Permission already granted.')
        getLocation()
      } else if (permission.state === 'prompt') {
        console.log('Permission is in prompt state. Requesting location...')
        getLocation()
      } else if (permission.state === 'denied') {
        alert('You have denied location access. Please enable it in browser settings.')
      }
    } catch (error) {
      console.error('Error checking geolocation permission:', error)
    }
  }, [getLocation])

  useEffect(() => {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          console.log('2' + position)
          setCurrentLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          })
        },
        () => {}
      )
    } else {
      //
    }
  }, [setCurrentLocation])

  return { requestLocationPermission }
}

export default useRequestLocationPermission
