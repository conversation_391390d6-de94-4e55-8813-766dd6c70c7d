import { useState } from 'react'

type ImageProps = {
  src: string
  alt?: string
  fallbackSrc?: string
  className?: string
}

export default function Image({
  src,
  alt = '',
  fallbackSrc = '/images/fallback.jpg',
  className = '',
  ...props
}: ImageProps) {
  const [imgSrc, setImgSrc] = useState(src)

  return (
    <img
      src={imgSrc}
      alt={alt}
      loading="lazy"
      onError={() => setImgSrc(fallbackSrc)}
      className={className}
      {...props}
    />
  )
}
