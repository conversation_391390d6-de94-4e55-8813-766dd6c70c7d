import logo from '@/../public/images/logo.png'
import Button from '@/components/Button'
import Image from '@/components/Image'
import { IconLogout } from '@/icons/icon-logout'
import useAuthStore from '@/stores/authStore'
import useViewMapStore from '@/stores/viewMapStore'
import { Link, useNavigate, useRouterState } from '@tanstack/react-router'
import clsx from 'clsx'

function Header() {
  const isLoggedIn = useAuthStore((state) => state.isAuthenticated)
  const pathname = useRouterState({ select: (state) => state.location.pathname })
  const { logout } = useAuthStore()
  const { initializeCurrentLocation } = useViewMapStore()
  const navigate = useNavigate()
  const { user } = useAuthStore()
  return (
    <header className="sticky top-0 z-50 h-[80px] bg-white text-white flex items-center px-[66px] header-shadow">
      <div className="p-2 flex gap-2 justify-between items-center w-full">
        <div className="flex items-center gap-8 justify-center">
          <Link to="/view-map" className="[&.active]:font-bold">
            <Image className="w-[132px]" src={logo} />
          </Link>{' '}
          {isLoggedIn && (
            <>
              <Link to="/view-map">
                <div
                  className={clsx('py-4 border-b-2 hover:border-b-primary cursor-pointer', {
                    'border-b-primary': pathname === '/view-map',
                  })}
                >
                  <p className="m-0 text-primary">View map</p>
                </div>
              </Link>
              <Link to="/report">
                <div
                  className={clsx('py-4 border-b-2 hover:border-b-primary cursor-pointer', {
                    'border-b-primary': pathname === '/report',
                  })}
                >
                  <p className="m-0 text-primary">Report</p>
                </div>
              </Link>
            </>
          )}
        </div>

        {isLoggedIn && (
          <div className="flex items-center gap-4">
            <p className="m-0 text-primary">{user?.fullName || '-------------'}</p>
            <Button
              rounded="full"
              icon={<IconLogout size={16} />}
              onClick={() => {
                logout()
                initializeCurrentLocation()
                navigate({ to: '/login' })
              }}
            >
              Logout
            </Button>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
