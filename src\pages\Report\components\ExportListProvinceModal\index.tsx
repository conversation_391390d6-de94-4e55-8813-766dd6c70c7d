import Button, { ButtonVariant } from '@/components/Button'
import CustomerDataExporter from '@/components/ExportDataButton'
import Modal from '@/components/Modal'
import { IViewMapReport } from '@/types'

function ExportListProvinceModal({
  isOpen,
  setIsOpen,
  data,
}: {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
  data: IViewMapReport[]
}) {
  return (
    <Modal open={isOpen} onOpenChange={setIsOpen}>
      <div className="w-full h-full flex flex-col items-center justify-center">
        <div className="py-4 flex justify-center items-center border-b-1 border-primary w-full">
          <p className="m-0 text-xl text-primary">List of provinces</p>
        </div>
        <div className="flex flex-col gap-2 mt-4 w-full">
          <div className="flex justify-between items-center w-full bg-neutral-200 py-3">
            <p className="m-0 text-base text-black w-[30%] text-center font-bold">No</p>
            <p className="m-0 text-base text-black w-[70%] text-center font-bold">Province</p>
          </div>
          <div className="overflow-y-auto max-h-[400px]">
            {data?.map((_, i) => (
              <div key={i} className="flex justify-between items-center w-full py-3">
                <p className="m-0 text-sm text-black w-[30%] text-center">{i + 1}</p>
                <p className="m-0 text-sm text-black w-[70%] text-center">{_.province}</p>
              </div>
            ))}
          </div>
          <p className="m-0 text-sm">
            Total: <span className="font-bold">{data?.length}</span> province
            {data?.length > 1 ? 's' : ''}
          </p>
        </div>
        <div className="w-full flex gap-2 justify-center items-center mt-4">
          <Button
            className="w-full"
            variant={ButtonVariant.NEUTRAL}
            onClick={() => setIsOpen(false)}
          >
            Close
          </Button>
          <CustomerDataExporter
            className="w-full"
            disabled={!data?.length}
            isReportViewMap
            columns={[
              { header: 'Province', key: 'province', width: 15 },
              { header: '< 3 months', key: 'lt3Months', width: 20 },
              { header: '< 6 months', key: 'lt6Months', width: 15 },
              { header: '< 12 months', key: 'lt12Months', width: 15 },
              { header: '> 12 months', key: 'gt12Months', width: 15 },
              { header: 'Newly activated this month', key: 'activeThisMonth', width: 15 },
              { header: 'Blocked for 1 month', key: 'block1Month', width: 15 },
              { header: 'Blocked for 2 months', key: 'block2Month', width: 15 },
              { header: 'Blocked for 3 months', key: 'block3Month', width: 15 },
              { header: 'Blocked over 3 months', key: 'blockOver3Month', width: 15 },
            ]}
            data={data.map((item) => ({
              province: item.province,
              ...item.reportCablebox,
              ...item.reportFtth,
            }))}
          />
        </div>
      </div>
    </Modal>
  )
}

export default ExportListProvinceModal
