import { useEffect, useRef, useState } from 'react'

export interface Option {
  value: string
  label: string
}

interface CustomSelectProps {
  options?: Option[]
  value: Option | Option[] | null | undefined
  onChange: (value: Option | Option[] | null) => void
  isMulti?: boolean
  isSearchable?: boolean
  placeholder?: string
  isClearable?: boolean
  isDisabled?: boolean
  loading?: boolean
  loadingMessage?: string
}

const CustomSelect = ({
  options = [],
  value,
  onChange,
  isMulti = false,
  isSearchable = false,
  placeholder = 'Select...',
  isClearable = false,
  isDisabled = false,
  loading: isLoading = false,
  loadingMessage = 'Loading...',
}: CustomSelectProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const selectRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (option: Option) => {
    if (isMulti) {
      const newValue = value ? [...(value as Option[])] : []
      if (newValue.some((item) => item.value === option.value)) {
        onChange(newValue.filter((item) => item.value !== option.value))
      } else {
        onChange([...newValue, option])
      }
    } else {
      onChange(option)
      setIsOpen(false)
    }
  }

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange(isMulti ? [] : null)
  }

  const removeOption = (e: React.MouseEvent, optionValue: string) => {
    e.stopPropagation()
    if (isMulti && value) {
      onChange((value as Option[]).filter((option) => option.value !== optionValue))
    }
  }

  const isSelected = (option: Option) => {
    if (isMulti && value) {
      return (value as Option[]).some((item) => item.value === option.value)
    }
    return (value as Option)?.value === option.value
  }

  return (
    <div className="relative w-full" ref={selectRef}>
      <div
        className={`flex items-center justify-between p-2 border rounded-md cursor-pointer ${isDisabled ? 'bg-gray-100 !cursor-not-allowed opacity-60' : 'bg-white hover:border-primary'} ${isOpen ? 'border-primary ring-1 ring-primary' : 'border-gray-300'}`}
        onClick={() => !isDisabled && setIsOpen(!isOpen)}
      >
        <div className="flex flex-wrap gap-1 flex-1">
          {!value || (isMulti && (value as Option[]).length === 0) ? (
            <span className="text-gray-400">{placeholder}</span>
          ) : isMulti ? (
            (value as Option[]).map((option) => (
              <span
                key={option.value}
                className="flex items-center gap-1 px-2 py-1 text-sm bg-blue-100 text-blue-800 rounded"
              >
                {option.label}
                <button
                  type="button"
                  onClick={(e) => removeOption(e, option.value)}
                  className="text-blue-500 hover:text-blue-700"
                >
                  ×
                </button>
              </span>
            ))
          ) : (
            <span className="text-gray-800">{(value as Option).label}</span>
          )}
        </div>
        <div className="flex items-center gap-1 ml-2">
          {isClearable && value && !isDisabled && (
            <button
              type="button"
              onClick={clearSelection}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          )}
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
          ) : (
            <svg
              className={`w-4 h-4 text-black transition-transform`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={4}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          )}
        </div>
      </div>

      {isOpen && !isDisabled && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          {isSearchable && (
            <div className="p-2 border-b border-gray-200">
              <input
                type="text"
                className="w-full p-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus
              />
            </div>
          )}
          <div className="py-1 overflow-auto max-h-60">
            {isLoading ? (
              <div className="flex justify-center items-center p-4">
                <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500">{loadingMessage}</span>
              </div>
            ) : filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <div
                  key={option.value}
                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 text-black ${isSelected(option) ? '!bg-primary !text-white' : ''}`}
                  onClick={() => handleSelect(option)}
                >
                  <div className="flex items-center">
                    {isMulti && (
                      <input
                        type="checkbox"
                        checked={isSelected(option)}
                        readOnly
                        className="mr-2 rounded text-blue-600 focus:ring-blue-500"
                      />
                    )}
                    {option.label}
                  </div>
                </div>
              ))
            ) : (
              <div className="px-3 py-2 text-gray-500">No options found</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomSelect
