import {
  ACCOUNT_FTTH,
  CABLE_BOX,
  LOGIN,
  PROVINC<PERSON>,
  VIEW_MAP_REPORT,
  VIEW_MAP_REPORT_CABLE_BOX_LIST,
  VIEW_MAP_REPORT_FTTH_ACCOUNT_LIST,
} from '@/constants/end-points'
import axiosInstance from '@/lib/axios'
import {
  IAccountFTTHRequestParams,
  IAccountFTTHResponse,
  ICableBoxRequestParams,
  ICableBoxResponse,
  ILoginResponse,
  IProvinceResponse,
  IViewMapReportCableboxParams,
  IViewMapReportCableboxResponse,
  IViewMapReportFtthAccountDetailResponse,
  IViewMapReportFtthAccountParams,
  IViewMapReportParams,
  IViewMapReportResponse,
} from '@/types'

export const login = (username: string, password: string, language?: 'en' | 'vi') =>
  axiosInstance.post<ILoginResponse>(LOGIN, {
    username,
    password,
    language: language || 'en',
  })

export const getListProvince = () => axiosInstance.get<IProvinceResponse>(PROVINCE)

export const getListCableBox = (params: ICableBoxRequestParams) =>
  axiosInstance.get<ICableBoxResponse>(CABLE_BOX, {
    params: params,
  })

export const getListAccountFTTH = (params?: IAccountFTTHRequestParams) =>
  axiosInstance.get<IAccountFTTHResponse>(ACCOUNT_FTTH, {
    params: params,
  })

export const getViewMapReportByProvince = (params: IViewMapReportParams) =>
  axiosInstance.get<IViewMapReportResponse>(VIEW_MAP_REPORT, {
    params: params,
  })

export const getViewMapReportCableBoxList = (params?: IViewMapReportCableboxParams) =>
  axiosInstance.get<IViewMapReportCableboxResponse>(VIEW_MAP_REPORT_CABLE_BOX_LIST, {
    params: params,
  })

export const getViewMapReportFtthAccountList = (params?: IViewMapReportFtthAccountParams) =>
  axiosInstance.get<IViewMapReportFtthAccountDetailResponse>(VIEW_MAP_REPORT_FTTH_ACCOUNT_LIST, {
    params: params,
  })
