import { memo } from 'react'

interface MapOverlayProps {
  radiusToPixel: number
  overlayRef: React.RefObject<HTMLDivElement>
}

export const MapOverlay = memo(({ radiusToPixel, overlayRef }: MapOverlayProps) => {
  return (
    <>
      <div
        ref={overlayRef}
        className="absolute top-0 left-0 w-full h-[109%] bg-black/50 pointer-events-none z-[100]"
        style={{
          mask: `radial-gradient(circle at center, transparent ${radiusToPixel}px, black ${radiusToPixel}px)`,
          WebkitMask: `radial-gradient(circle at center, transparent ${radiusToPixel}px, black ${radiusToPixel}px)`,
        }}
      ></div>
      <div
        className="absolute z-[101] border-4 border-primary rounded-full pointer-events-none"
        style={{
          width: `${radiusToPixel * 2}px`,
          height: `${radiusToPixel * 2}px`,
          left: `calc(50% - ${radiusToPixel}px)`,
          top: `calc(54.5% - ${radiusToPixel}px)`,
        }}
      ></div>
    </>
  )
})
