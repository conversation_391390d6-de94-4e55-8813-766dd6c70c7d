function LoadingSync({ percentage }: { percentage: number }) {
  return (
    <div className="absolute top-0 left-0 w-full h-[100%] bg-black/50 pointer-events-none z-[100] flex items-end justify-center">
      <div className="w-full h-[70px] bg-white bottom-0 px-8 py-4">
        <p className="m-0 text-base font-normal italic">
          Loading FTTH accounts... Please wait. {percentage}%
        </p>
        <div className="w-full h-1 bg-gray-200 mt-2">
          <div className="h-1 bg-primary" style={{ width: `${percentage}%` }}></div>
        </div>
      </div>
    </div>
  )
}

export default LoadingSync
