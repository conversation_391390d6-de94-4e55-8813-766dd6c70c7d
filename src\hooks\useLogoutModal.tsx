import Button from '@/components/Button'
import Modal from '@/components/Modal'
import useAuthStore from '@/stores/authStore'
import { useNavigate } from '@tanstack/react-router'
import { useState } from 'react'

interface ModalHookReturn {
  LogoutModal: React.FC<{ children: React.ReactNode }>
  openModal: () => void
  closeModal: () => void
  isOpen: boolean
}

export const useLogoutModal = (): ModalHookReturn => {
  const { logout } = useAuthStore()
  const navigate = useNavigate()

  const [isOpen, setIsOpen] = useState(false)

  const openModal = () => setIsOpen(true)
  const closeModal = () => setIsOpen(false)

  const LogoutModal = () => {
    if (!isOpen) return null

    return (
      <Modal open={isOpen} onOpenChange={closeModal} contentClassName="!max-w-[500px]">
        <div className="w-full h-full flex flex-col items-center justify-center">
          <div className="py-4 flex justify-center items-center w-full">
            <p className="m-0 text-xl text-primary">Session is expired, please login again!</p>
          </div>
          <div className="w-full flex gap-2 justify-center items-center mt-4">
            <Button
              className="w-full"
              onClick={() => {
                logout()
                navigate({ to: '/login' })
                closeModal()
              }}
            >
              Logout
            </Button>
          </div>
        </div>
      </Modal>
    )
  }

  return { LogoutModal, openModal, closeModal, isOpen }
}
