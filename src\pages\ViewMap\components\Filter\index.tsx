import NotFoundImg from '@/../public/images/not_found.png'
import Button, { ButtonSize, ButtonVariant } from '@/components/Button'
import CustomCheckbox from '@/components/CheckBox'
import Image from '@/components/Image'
import Modal from '@/components/Modal'
import { DEFAULT_FILTER } from '@/constants/view-map'
import {
  Icon0Account,
  Icon1Account,
  Icon2Account,
  IconCPUBlue,
  IconCPUGreen,
  IconCPURed,
  IconCPUYellow,
  IconFilter,
} from '@/icons'
import { useMemo, useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import './style.css'

export type FormFilterValues = {
  '0_account': boolean
  '1_account': boolean
  '2_account': boolean
  block_1_m: boolean
  block_2_m: boolean
  block_3_m: boolean
  terminate_block_over_3_m: boolean
  new_active: boolean
  less_3_m: boolean
  less_6_m: boolean
  less_12_m: boolean
  over_12_m: boolean
}

const FTTHAccount: { name: keyof FormFilterValues; label: React.ReactNode }[] = [
  {
    name: '0_account',
    label: (
      <div className="flex items-center">
        <Icon0Account />
        <span className="ml-2 text-sm text-text-color">No mobile account</span>
      </div>
    ),
  },
  {
    name: '1_account',
    label: (
      <div className="flex items-center">
        <Icon1Account />
        <span className="ml-2 text-sm text-text-color">1 mobile account</span>
      </div>
    ),
  },
  {
    name: '2_account',
    label: (
      <div className="flex items-center">
        <Icon2Account />
        <span className="ml-2 text-sm text-text-color">2 mobile accounts</span>
      </div>
    ),
  },
]

const StatusFTTHAccount: { name: keyof FormFilterValues; label: React.ReactNode }[] = [
  {
    name: 'block_1_m',
    label: (
      <div className="flex items-center">
        <div className="w-[10px] h-[10px] bg-[#DA3FC5]"></div>
        <span className="ml-2 text-sm text-text-color">Blocked for 1 month</span>
      </div>
    ),
  },
  {
    name: 'block_2_m',
    label: (
      <div className="flex items-center">
        <div className="w-[10px] h-[10px] bg-[#C41627]"></div>
        <span className="ml-2 text-sm text-text-color">Blocked for 2 months</span>
      </div>
    ),
  },
  {
    name: 'block_3_m',
    label: (
      <div className="flex items-center">
        <div className="w-[10px] h-[10px] bg-[#1BBD3E]"></div>
        <span className="ml-2 text-sm text-text-color">Blocked for 3 months</span>
      </div>
    ),
  },
  {
    name: 'terminate_block_over_3_m',
    label: (
      <div className="flex items-center">
        <div className="w-[10px] h-[10px] bg-[#F1DD49]"></div>
        <span className="ml-2 text-sm text-text-color">Terminate & Blocked over 3 months</span>
      </div>
    ),
  },
  {
    name: 'new_active',
    label: (
      <div className="flex items-center">
        <div className="w-[10px] h-[10px] bg-[#38D9D6]"></div>
        <span className="ml-2 text-sm text-text-color">Newly activated this month</span>
      </div>
    ),
  },
]

const UsageTimeCableBox: { name: keyof FormFilterValues; label: React.ReactNode }[] = [
  {
    name: 'less_3_m',
    label: (
      <div className="flex items-center">
        <IconCPURed />
        <span className="ml-2 text-sm text-text-color">Less than 3 months</span>
      </div>
    ),
  },
  {
    name: 'less_6_m',
    label: (
      <div className="flex items-center">
        <IconCPUYellow />
        <span className="ml-2 text-sm text-text-color">Less than 6 months</span>
      </div>
    ),
  },
  {
    name: 'less_12_m',
    label: (
      <div className="flex items-center">
        <IconCPUBlue />
        <span className="ml-2 text-sm text-text-color">Less than 12 months</span>
      </div>
    ),
  },
  {
    name: 'over_12_m',
    label: (
      <div className="flex items-center">
        <IconCPUGreen />
        <span className="ml-2 text-sm text-text-color">Over 12 months</span>
      </div>
    ),
  },
]

function Filter({ onSubmitFilter }: { onSubmitFilter: (dataFilter: FormFilterValues) => number }) {
  const { control, handleSubmit, watch } = useForm<FormFilterValues>({
    defaultValues: DEFAULT_FILTER,
  })
  const [isOpen, setIsOpen] = useState(false)

  const [isModalOpen, setIsModalOpen] = useState(false)

  const onSubmit: SubmitHandler<FormFilterValues> = (data) => {
    const result = onSubmitFilter(data)
    setIsModalOpen(Boolean(result))
    setIsOpen(false)
  }

  const watchFTTHStatusFields = watch([
    'block_1_m',
    'block_2_m',
    'block_3_m',
    'terminate_block_over_3_m',
    'new_active',
  ])

  const watchFTTHTypeFields = watch(['0_account', '1_account', '2_account'])

  const isDisableButton = useMemo(() => {
    if (
      (watchFTTHStatusFields.some((value) => value) &&
        !watchFTTHTypeFields.some((value) => value)) ||
      (!watchFTTHStatusFields.some((value) => value) && watchFTTHTypeFields.some((value) => value))
    ) {
      return true
    }
    return false
  }, [watchFTTHStatusFields, watchFTTHTypeFields])

  return (
    <div className="relative">
      <Button
        shadow
        className="h-full"
        size={ButtonSize.SM}
        variant={ButtonVariant.OUTLINE_NEUTRAL}
        rounded="full"
        onClick={() => {
          setIsOpen((prev) => !prev)
        }}
        icon={<IconFilter />}
      >
        Filters
      </Button>
      {isOpen && (
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="popupWrapper">
            <div className="absolute bg-white rounded-2xl p-6 shadow-md w-[418px] flex flex-col gap-5 right-0 top-15 z-[15] max-h-[calc(100vh-170px)] overflow-auto">
              <div className="flex flex-col gap-4">
                <p className="m-0 text-base font-normal">
                  Type of FTTH account containing mobile account
                </p>
                {FTTHAccount.map((item) => (
                  <CustomCheckbox
                    key={item.name}
                    control={control}
                    name={item.name}
                    label={item.label}
                  />
                ))}
              </div>
              <div className="flex flex-col gap-4">
                <p className="m-0 text-base font-normal">Status of FTTH account</p>
                {StatusFTTHAccount.map((item) => (
                  <CustomCheckbox
                    key={item.name}
                    control={control}
                    name={item.name}
                    label={item.label}
                  />
                ))}
              </div>
              <div className="flex flex-col gap-4">
                <p className="m-0 text-base font-normal">Usage time of Cable box</p>
                {UsageTimeCableBox.map((item) => (
                  <CustomCheckbox
                    key={item.name}
                    control={control}
                    name={item.name}
                    label={item.label}
                  />
                ))}
              </div>
              <Button
                className="w-full"
                variant={ButtonVariant.PRIMARY}
                size={ButtonSize.SM}
                disabled={isDisableButton}
              >
                Apply
              </Button>
            </div>
          </div>
        </form>
      )}
      <Modal open={isModalOpen} onOpenChange={() => {}}>
        <div className="flex flex-col gap-4 justify-center items-center">
          <Image src={NotFoundImg} alt="not found" />
          <Button className="w-[40%]" onClick={() => setIsModalOpen(false)}>
            Go back
          </Button>
        </div>
      </Modal>
    </div>
  )
}

export default Filter
