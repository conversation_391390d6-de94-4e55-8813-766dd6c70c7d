import { EyeIcon } from '@/icons/icon-eye'
import { EyeOffIcon } from '@/icons/icon-eye-off'
import clsx from 'clsx'
import React, { useState } from 'react'

type InputProps = React.InputHTMLAttributes<HTMLInputElement> & {
  label?: string
  error?: string
  className?: string
  containerClassName?: string
  isHiddenErrorMessage?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, type = 'text', className, isHiddenErrorMessage = false, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false)
    const isPassword = type === 'password'
    return (
      <div className={clsx('flex flex-col gap-2', className)}>
        {label && <label className="text-sm font-medium text-gray-700">{label}</label>}

        <div className="relative">
          <input
            ref={ref}
            type={isPassword ? (showPassword ? 'text' : 'password') : type}
            className={clsx(
              'px-3 py-2 border rounded-md text-sm w-full transition-all pr-10 focus:border-primary focus:ring-primary focus:outline-none focus:ring-0 hover:border-primary',
              error ? 'border-red-500' : 'border-gray-300',
              className
            )}
            {...props}
          />

          {isPassword && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-gray-500 cursor-pointer"
              tabIndex={-1}
            >
              {showPassword ? <EyeOffIcon /> : <EyeIcon />}
            </button>
          )}
        </div>

        {error && !isHiddenErrorMessage && <p className="text-red-500 text-sm mt-1">{error}</p>}
      </div>
    )
  }
)

Input.displayName = 'Input'

export default Input
