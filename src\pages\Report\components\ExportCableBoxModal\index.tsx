import { getViewMapReportCableBoxList } from '@/api'
import Button, { ButtonVariant } from '@/components/Button'
import CustomerDataExporter from '@/components/ExportDataButton'
import Modal from '@/components/Modal'
import Spinner from '@/components/Spinner'
import { EViewMapReportCableBoxStatus } from '@/types'
import { formatDateToDDMMYYYY } from '@/utils'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useMemo } from 'react'
import { ModalInfo } from '../..'

const STATUS_MAPPING: Record<EViewMapReportCableBoxStatus, string> = {
  0: '< 3 months',
  1: '< 6 months',
  2: '< 12 months',
  3: '> 12 months',
}

function ExportCableBoxModal({
  isOpen,
  onCloseModal,
  modalInfo,
}: {
  isOpen: boolean
  onCloseModal: () => void
  modalInfo: ModalInfo | null
}) {
  const {
    refetch,
    isLoading,
    data: response,
  } = useQuery({
    queryKey: [
      'reportCableBoxDetailList',
      {
        params: {
          province: modalInfo?.province?.code || '',
          status: (modalInfo?.status as EViewMapReportCableBoxStatus) || 0,
          reportType: modalInfo?.type || 0,
        },
      },
    ],
    queryFn: async () =>
      await getViewMapReportCableBoxList({
        province: modalInfo?.province?.code || '',
        status: (modalInfo?.status as EViewMapReportCableBoxStatus) || 0,
        reportType: modalInfo?.type || 0,
      }),
    enabled: false,
  })

  const dataReport = useMemo(() => {
    return response?.data?.cableboxs || []
  }, [response?.data?.cableboxs])

  const dataExport = useMemo(() => {
    return (
      dataReport.map((item, idx) => ({
        no: idx + 1,
        province: modalInfo?.province?.name ?? '',
        name: item.cableBox,
        createDate: formatDateToDDMMYYYY(item.createDate),
        usageVolume:
          item.totalCount +
          '/' +
          item.target +
          ` (${((item.totalCount / item.target) * 100).toFixed(2)}%)`,
      })) || []
    )
  }, [dataReport, modalInfo?.province?.name])

  useEffect(() => {
    if (modalInfo?.province?.code) {
      refetch()
    }
  }, [modalInfo?.province?.code, refetch])
  return (
    <Modal open={isOpen} onOpenChange={onCloseModal} contentClassName="!max-w-[626px]">
      <div className="w-full h-full flex flex-col items-center justify-center">
        <div className="py-4 flex justify-center items-center border-b-1 border-primary w-full">
          <p className="m-0 text-xl text-primary">List of cable boxes</p>
        </div>
        <div className="flex flex-col gap-2 mt-4 w-full">
          <p className="m-0 text-base">
            Province: <span className="text-primary">{modalInfo?.province?.name ?? ''}</span>
          </p>
          <p className="m-0 text-base">
            Usage time:{' '}
            <span className="text-primary">
              {STATUS_MAPPING[(modalInfo?.status as EViewMapReportCableBoxStatus) ?? 0]}
            </span>
          </p>
        </div>
        <div className="flex flex-col gap-2 mt-4 w-full">
          <div className="flex justify-between items-center w-full bg-neutral-200 py-3">
            <p className="m-0 text-base font-bold text-black w-[30%] text-center">No</p>
            <p className="m-0 text-base font-bold text-black w-[70%] text-center">Name</p>
            <p className="m-0 text-base font-bold text-black w-[70%] text-center">Create Date</p>
            <p className="m-0 text-base font-bold text-black w-[70%] text-center">Usage Volume</p>
          </div>
          <div className="overflow-y-auto max-h-[400px]">
            {isLoading ? (
              <div className="w-full flex justify-center items-center">
                <Spinner />
              </div>
            ) : (
              <>
                {dataReport.length > 0 ? (
                  dataReport.map((_, i) => (
                    <div key={i} className="flex justify-between items-center w-full py-3">
                      <p className="m-0 text-sm text-black w-[30%] text-center">{i + 1}</p>
                      <p className="m-0 text-sm text-black w-[70%] text-center">{_.cableBox}</p>
                      <p className="m-0 text-sm text-black w-[70%] text-center">
                        {formatDateToDDMMYYYY(_.createDate)}
                      </p>
                      <p className="m-0 text-sm text-black w-[70%] text-center">
                        {_.totalCount + '/' + _.target} (
                        {((_.totalCount / _.target) * 100).toFixed(2)}%)
                      </p>
                    </div>
                  ))
                ) : (
                  <p className="m-0 text-sm text-center text-gray-400">No data found</p>
                )}
              </>
            )}
          </div>
          <p className="m-0 text-sm">
            Total: <span className="font-bold">{dataReport?.length}</span> cable box
            {dataReport?.length > 1 ? 'es' : ''}
          </p>
        </div>
        <div className="w-full flex gap-2 justify-center items-center mt-4">
          <Button className="w-full" variant={ButtonVariant.NEUTRAL} onClick={onCloseModal}>
            Close
          </Button>
          <CustomerDataExporter
            className="w-full"
            disabled={!dataReport?.length || isLoading}
            columns={[
              { header: 'No', key: 'no', width: 15 },
              { header: 'Province', key: 'province', width: 15 },
              { header: 'Cable box name', key: 'name', width: 20 },
              { header: 'Create date', key: 'createDate', width: 15 },
              { header: 'Usage volume (%)', key: 'usageVolume', width: 15 },
            ]}
            data={dataExport}
          />
        </div>
      </div>
    </Modal>
  )
}

export default ExportCableBoxModal
