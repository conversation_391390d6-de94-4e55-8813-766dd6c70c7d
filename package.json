{"name": "view-map", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint src --ext .js,.jsx --fix", "preview": "vite preview"}, "dependencies": {"@googlemaps/markerclusterer": "^2.5.3", "@radix-ui/react-dialog": "^1.1.7", "@react-google-maps/api": "^2.20.6", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.71.10", "@tanstack/react-router": "^1.114.34", "axios": "^1.8.4", "file-saver": "^2.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-paginate": "^8.3.0", "react-select": "^5.10.1", "tailwindcss": "^4.1.3", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tanstack/react-router-devtools": "^1.114.34", "@tanstack/router-plugin": "^1.114.34", "@types/file-saver": "^2.0.7", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.15.0", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}