import { IconDoubleArrowLeft } from '@/icons'
import clsx from 'clsx'
import ReactPaginate from 'react-paginate'
import './styles.css'

interface PaginationProps {
  currentPage: number
  pageCount: number
  onPageChange: (selectedItem: { selected: number }) => void
}

const Pagination = ({ currentPage, pageCount, onPageChange }: PaginationProps) => {
  return (
    <div className="flex items-center gap-2">
      <div
        className={clsx('page-item', {
          disabled: currentPage === 1,
        })}
      >
        <div className="page-link" onClick={() => onPageChange({ selected: 0 })}>
          <IconDoubleArrowLeft />
        </div>
      </div>
      <ReactPaginate
        breakLabel="..."
        nextLabel={
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.5 15L12.5 10L7.5 5"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        }
        previousLabel={
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12.5 15L7.5 10L12.5 5"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        }
        onPageChange={onPageChange}
        pageRangeDisplayed={3}
        marginPagesDisplayed={1}
        pageCount={pageCount}
        forcePage={currentPage - 1}
        renderOnZeroPageCount={null}
        className="pagination"
        pageClassName="page-item"
        pageLinkClassName="page-link"
        previousClassName="page-item"
        previousLinkClassName="page-link"
        nextClassName="page-item"
        nextLinkClassName="page-link"
        breakClassName="page-item"
        breakLinkClassName="page-link"
        activeClassName="active"
      />
      <div
        className={clsx('page-item', {
          disabled: currentPage === pageCount,
        })}
      >
        <div className="page-link" onClick={() => onPageChange({ selected: pageCount - 1 })}>
          <div className="rotate-180">
            <IconDoubleArrowLeft />
          </div>
        </div>
      </div>
    </div>
  )
}

export default Pagination
