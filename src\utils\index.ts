export const getDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371000 // <PERSON><PERSON>h tr<PERSON>i đất (m)
  const dLat = ((lat2 - lat1) * Math.PI) / 180
  const dLng = ((lng2 - lng1) * Math.PI) / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c // Khoảng cách tính bằng mét
}

export function formatDateToDDMMYYYY(dateString: string): string {
  const date = new Date(dateString)
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string')
  }

  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()

  return `${day}/${month}/${year}`
}

export function pixelHeightToRadius(pixelHeight: number, lat: number, zoomLevel: number) {
  // Constants
  const EARTH_CIRCUMFERENCE = 40075017 // in meters
  const TILE_SIZE = 256 // standard map tile size in pixels

  // Calculate the scale factor based on zoom level
  // This assumes a standard Web Mercator projection where scale = 2^zoomLevel
  const scale = Math.pow(2, zoomLevel)

  // Calculate the radius in meters
  const radius =
    (pixelHeight * EARTH_CIRCUMFERENCE) / (scale * TILE_SIZE * Math.cos((lat * Math.PI) / 180))

  return radius
}

export function googleMapDirectionURL({
  currentLocation,
  targetLocation,
}: {
  currentLocation: { lat: number; lng: number }
  targetLocation: { lat: number; lng: number }
}) {
  return `https://www.google.com/maps/dir/?api=1&origin=${currentLocation.lat},${currentLocation.lng}&destination=${targetLocation.lat},${targetLocation.lng}&travelmode=driving`
}

export function googleMapLocationURL({ lat, lng }: { lat: number; lng: number }) {
  return `https://www.google.com/maps?q=${lat},${lng}+(Target)`
}
