import React from 'react'
import { Control, FieldPathValue, FieldValues, Path, useController } from 'react-hook-form'

interface CustomCheckboxProps<T extends FieldValues> {
  name: Path<T>
  control: Control<T>
  label?: string | React.ReactNode
  className?: string
}

const CustomCheckbox = <T extends FieldValues>({
  name,
  control,
  label,
  className = '',
}: CustomCheckboxProps<T>) => {
  const {
    field: { value, onChange, ref },
  } = useController<T, Path<T>>({
    name,
    control,
    defaultValue: false as FieldPathValue<T, Path<T>>,
  })

  return (
    <label className={`inline-flex items-center gap-2 cursor-pointer w-fit ${className}`}>
      {/* Hidden real checkbox */}
      <input
        type="checkbox"
        checked={value}
        onChange={(e) => onChange(e.target.checked)}
        name={name}
        ref={ref}
        className="hidden"
      />

      {/* Custom visual */}
      <div
        className={`w-5 h-5 flex items-center justify-center border-1 rounded transition-colors duration-200 ${
          value ? 'bg-border-color border-border-color' : 'bg-white border-border-color'
        }`}
      >
        {value && (
          <svg
            className="w-4 h-4 text-white"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth={3}
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="20 6 9 17 4 12" />
          </svg>
        )}
      </div>

      {label && <span className="text-sm text-text-color">{label}</span>}
    </label>
  )
}

export default CustomCheckbox
