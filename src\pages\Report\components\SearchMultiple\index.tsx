/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useRef } from 'react'
import Select, { components, MenuListProps, StylesConfig } from 'react-select'

export type OptionType = {
  value: string
  label: string
}

type MultiSelectProps = {
  options: OptionType[]
  value: OptionType[] | undefined
  onChange: (selected: OptionType[]) => void
  placeholder?: string
  isDisabled?: boolean
  isClearable?: boolean
  loading?: boolean
}

// Custom Option component with checkbox
const Option = (props: any) => {
  return (
    <components.Option {...props}>
      <div className="flex items-center gap-2">
        <div
          className={`w-5 h-5 flex items-center justify-center border-1 rounded transition-colors duration-200 ${
            props.isSelected
              ? 'bg-border-color border-border-color'
              : 'bg-white border-border-color'
          }`}
        >
          {props.isSelected && (
            <svg
              className="w-4 h-4 text-white"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth={3}
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="20 6 9 17 4 12" />
            </svg>
          )}
        </div>
        <span className="truncate">{props.label}</span>
      </div>
    </components.Option>
  )
}

// Custom MultiValue component to hide chips
const MultiValue = () => {
  return null
}

// Custom ValueContainer with ellipsis
const ValueContainer = ({ children, ...props }: any) => {
  const { getValue } = props
  const values = getValue()
  const valueLabel = values.map((v: OptionType) => v.label).join(', ')

  return (
    <components.ValueContainer {...props}>
      <div className="truncate max-w-full h-[70%]">{values.length > 0 ? valueLabel : children}</div>
    </components.ValueContainer>
  )
}

const CustomMenuList: React.FC<MenuListProps<any, boolean>> = (props) => (
  <components.MenuList {...props}>{props.children}</components.MenuList>
)

const customStyles: StylesConfig<OptionType, true> = {
  control: (base, state) => ({
    ...base,
    backgroundColor: state.isDisabled ? 'var(--color-neutral-200)' : 'white',
    borderColor: state.isFocused ? 'var(--color-primary)' : '#d1d5db',
    boxShadow: state.isFocused ? '0 0 0 1px var(--color-primary)' : 'none',
    '&:hover': '#d1d5db',
    minHeight: '40px',
    maxHeight: '40px',
    cursor: state.isDisabled ? 'not-allowed' : 'pointer',
    opacity: state.isDisabled ? 0.6 : 1,
    border: 'none',
    borderBottom: '1px solid #d1d5db',
  }),
  option: (base, state) => ({
    ...base,
    backgroundColor: state.isFocused ? '#e0f2fe' : 'white',
    color: '#111827',
    cursor: 'pointer',
    padding: '8px 12px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    height: '40px',
    lineHeight: '25px',
  }),
  menu: (base) => ({
    ...base,
    borderRadius: '0.5rem',
    zIndex: 50,
  }),
  indicatorSeparator: () => ({
    display: 'none',
  }),
  dropdownIndicator: (base) => ({
    ...base,
    color: 'var(--color-primary)',
    padding: '0 8px',
  }),
  valueContainer: (base) => ({
    ...base,
    padding: '2px 8px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    height: '36px',
    display: 'flex',
    alignItems: 'center',
  }),
  input: (base) => ({
    ...base,
    maxHeight: '36px',
  }),
  placeholder: (base) => ({
    ...base,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  }),
  menuList: (base) => ({
    ...base,
    paddingTop: 0,
    paddingBottom: 0,
    maxHeight: '250px',
    overflowY: 'auto',
  }),
}

const MultiSelect = ({
  options,
  value,
  onChange,
  placeholder = 'Select...',
  isDisabled = false,
  isClearable = false,
  loading = false,
}: MultiSelectProps) => {
  const selectRef = useRef<any>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.controlRef.contains(event.target as Node) &&
        !document.querySelector('.select__menu')?.contains(event.target as Node)
      ) {
        selectRef.current.onMenuClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <Select
      ref={selectRef}
      isMulti
      menuPosition="fixed"
      className="w-full max-w-[285px]"
      classNamePrefix="select"
      styles={customStyles}
      options={options}
      value={value}
      onChange={(selected) => onChange(selected as OptionType[])}
      placeholder={placeholder}
      isDisabled={isDisabled || loading}
      isClearable={isClearable}
      isLoading={loading}
      hideSelectedOptions={false}
      closeMenuOnSelect={false}
      components={{
        Option,
        MultiValue,
        ValueContainer,
        MenuList: CustomMenuList,
      }}
      menuShouldScrollIntoView={false}
    />
  )
}

export default MultiSelect
