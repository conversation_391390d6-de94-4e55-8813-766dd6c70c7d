import cpuBlue from '@/../public/images/cpu_blue.png'
import cpuGreen from '@/../public/images/cpu_green.png'
import cpuRed from '@/../public/images/cpu_red.png'
import cpuYellow from '@/../public/images/cpu_yellow.png'
import { FormFilterValues } from '@/pages/ViewMap/components/Filter'

export const CABLE_BOX_STATUS = {
  LESS_3_MONTHS: {
    value: 0,
    label: 'Less than 3 months',
    color: '#FF0000', // Red
    icon: cpuRed,
  },
  LESS_6_MONTHS: {
    value: 1,
    label: 'Less than 6 months',
    color: '#FFFF00', //
    icon: cpuYellow,
  },
  LESS_12_MONTHS: {
    value: 2,
    label: 'Less than 12 months',
    color: '#0000FF', // Blue
    icon: cpuBlue,
  },
  OVER_12_MONTHS: {
    value: 3,
    label: 'Over 12 months',
    color: '#00FF00', // Green
    icon: cpuGreen,
  },
} as const

export type AccountStatusType = keyof typeof CABLE_BOX_STATUS

// Helper function to get status color by value
export const getStatusColor = (value: number): string => {
  const status = Object.values(CABLE_BOX_STATUS).find((status) => status.value === value)
  return status?.color || '#CCCCCC' // Default gray if not found
}

// Helper function to get status label by value
export const getStatusLabel = (value: number): string => {
  const status = Object.values(CABLE_BOX_STATUS).find((status) => status.value === value)
  return status?.label || 'Unknown Status'
}

// Helper function to get status icon by value
export const getStatusIcon = (value: number): string => {
  const status = Object.values(CABLE_BOX_STATUS).find((status) => status.value === value)
  return status?.icon || cpuRed // Default red icon if not found
}

export const FTTH_ACCOUNT_STATUS = {
  BLOCK_1_MONTH: {
    value: 1,
    label: 'Block for 1 month',
    color: '#DA3FC5', // Pink
  },
  BLOCK_2_MONTHS: {
    value: 2,
    label: 'Block for 2 months',
    color: '#C41627', // Red
  },
  BLOCK_3_MONTHS: {
    value: 3,
    label: 'Block for 3 months',
    color: '#1BBD3E', // Green
  },
  TERMINATE_BLOCK_OVER_3_MONTHS: {
    value: 4,
    label: 'Terminate & Blocked over 3 Months',
    color: '#F1DD49', // Yellow
  },
  NEW_ACTIVE: {
    value: 0,
    label: 'Newly activated this month',
    color: '#38D9D6', // Cyan
  },
} as const

export type FTTHAccountStatusType = keyof typeof FTTH_ACCOUNT_STATUS

// Helper function to get FTTH status color by value
export const getFTTHStatusColor = (value: number): string => {
  const status = Object.values(FTTH_ACCOUNT_STATUS).find((status) => status.value === value)
  return status?.color || '#CCCCCC' // Default gray if not found
}

// Helper function to get FTTH status label by value
export const getFTTHStatusLabel = (value: number): string => {
  const status = Object.values(FTTH_ACCOUNT_STATUS).find((status) => status.value === value)
  return status?.label || 'Unknown Status'
}

const CustomAccoutType0 = (color: string) =>
  `<svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="5" cy="5" r="5" fill="${color}" />
  </svg>`

const CustomAccoutType1 = (color: string) =>
  `<svg width="11" height="10" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.06132 8.49389C10.991 6.51855 10.9539 3.35294 8.97855 1.42331C7.0032 -0.506319 3.8376 -0.46926 1.90797 1.50609C-0.0216632 3.48143 0.015396 6.64704 1.99074 8.57667C3.96609 10.5063 7.13169 10.4692 9.06132 8.49389ZM5.45036 2.00099C5.24327 2.00341 5.07735 2.17326 5.07978 2.38035L5.10589 4.6302L2.85627 4.65654C2.64918 4.65896 2.48326 4.82881 2.48569 5.0359C2.48811 5.24299 2.65796 5.40891 2.86505 5.40649L5.11467 5.38015L5.14123 7.62999C5.14366 7.83709 5.31351 8.003 5.5206 8.00058L5.5643 7.99754C5.75053 7.9737 5.89343 7.81351 5.89118 7.62121L5.86463 5.37137L8.11469 5.34503C8.32178 5.3426 8.4877 5.17276 8.48527 4.96566C8.48285 4.75857 8.313 4.59266 8.10591 4.59508L5.85585 4.62142L5.82973 2.37157C5.8273 2.16448 5.65745 1.99857 5.45036 2.00099Z" fill="${color}"/>
  </svg>`
const CustomAccoutType2 = (color: string) =>
  `<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M5.94142 8.5502L3.19313 9.97604C3.15636 9.99511 3.11109 9.98077 3.09202 9.944C3.08455 9.92961 3.08195 9.91318 3.0846 9.89719L3.59138 6.84279C3.59804 6.80268 3.58477 6.76184 3.55581 6.7333L1.35048 4.56012C1.32098 4.53105 1.32063 4.48356 1.3497 4.45406C1.36109 4.44251 1.3759 4.43496 1.39194 4.43254L4.45344 3.97066C4.49365 3.96459 4.52839 3.93935 4.54659 3.90299L5.93192 1.13405C5.95045 1.09701 5.9955 1.082 6.03255 1.10053C6.04705 1.10779 6.05881 1.11955 6.06606 1.13405L7.4514 3.90299C7.46959 3.93935 7.50433 3.96459 7.54454 3.97066L10.606 4.43254C10.647 4.43872 10.6752 4.47693 10.669 4.51789C10.6666 4.53392 10.659 4.54874 10.6475 4.56012L8.44217 6.7333C8.41321 6.76184 8.39994 6.80268 8.4066 6.84279L8.91338 9.89719C8.92016 9.93805 8.89253 9.97667 8.85167 9.98345C8.83567 9.9861 8.81924 9.9835 8.80485 9.97604L6.05656 8.5502C6.02046 8.53147 5.97752 8.53147 5.94142 8.5502Z"
      fill="${color}"
      stroke="${color}"
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
  </svg>`

const iconUrl = (svgString: string) =>
  `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svgString)}`

export const FTTH_ACCOUNT_TYPE = {
  NO_ACCOUNT: {
    value: 0,
    label: 'No mobile account',
    icon: (color: string) => iconUrl(CustomAccoutType0(color)),
  },
  ONE_ACCOUNT: {
    value: 1,
    label: '1 mobile account',
    icon: (color: string) => iconUrl(CustomAccoutType1(color)),
  },
  TWO_ACCOUNT: {
    value: 2,
    label: '2 mobile account',
    icon: (color: string) => iconUrl(CustomAccoutType2(color)),
  },
}

export type FTTHAccountType = keyof typeof FTTH_ACCOUNT_TYPE

export const getFTTHAccountIcon = (value: number, color: string): string => {
  const account = Object.values(FTTH_ACCOUNT_TYPE).find((account) => account.value === value)
  return account?.icon(color) || iconUrl(CustomAccoutType0(color)) // Default red icon if not found
}

type FilterOptions = {
  less_3_m: boolean
  less_6_m: boolean
  less_12_m: boolean
  over_12_m: boolean
}

export const buildFilterCableBox = (options: FilterOptions, isAppliedFilter: boolean): number[] => {
  const filterMap = {
    less_3_m: CABLE_BOX_STATUS.LESS_3_MONTHS.value,
    less_6_m: CABLE_BOX_STATUS.LESS_6_MONTHS.value,
    less_12_m: CABLE_BOX_STATUS.LESS_12_MONTHS.value,
    over_12_m: CABLE_BOX_STATUS.OVER_12_MONTHS.value,
  } as const

  if (isAppliedFilter) {
    return (
      Object.entries(options)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        .filter(([_, isSelected]) => isSelected)
        .map(([key]) => filterMap[key as keyof FilterOptions])
    )
  }

  return [
    CABLE_BOX_STATUS.LESS_3_MONTHS.value,
    CABLE_BOX_STATUS.LESS_6_MONTHS.value,
    CABLE_BOX_STATUS.LESS_12_MONTHS.value,
    CABLE_BOX_STATUS.OVER_12_MONTHS.value,
  ]
}

type FilterFTTHStatusOptions = {
  block_1_m: boolean
  block_2_m: boolean
  block_3_m: boolean
  terminate_block_over_3_m: boolean
  new_active: boolean
}

export const buildFilterFTTHStatus = (
  options: FilterFTTHStatusOptions,
  isAppliedFilter: boolean
): number[] => {
  const filterMap = {
    block_1_m: FTTH_ACCOUNT_STATUS.BLOCK_1_MONTH.value,
    block_2_m: FTTH_ACCOUNT_STATUS.BLOCK_2_MONTHS.value,
    block_3_m: FTTH_ACCOUNT_STATUS.BLOCK_3_MONTHS.value,
    terminate_block_over_3_m: FTTH_ACCOUNT_STATUS.TERMINATE_BLOCK_OVER_3_MONTHS.value,
    new_active: FTTH_ACCOUNT_STATUS.NEW_ACTIVE.value,
  } as const

  if (isAppliedFilter) {
    return (
      Object.entries(options)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        .filter(([_, isSelected]) => isSelected)
        .map(([key]) => filterMap[key as keyof FilterFTTHStatusOptions])
    )
  }

  return [
    FTTH_ACCOUNT_STATUS.BLOCK_1_MONTH.value,
    FTTH_ACCOUNT_STATUS.BLOCK_2_MONTHS.value,
    FTTH_ACCOUNT_STATUS.BLOCK_3_MONTHS.value,
    FTTH_ACCOUNT_STATUS.TERMINATE_BLOCK_OVER_3_MONTHS.value,
    FTTH_ACCOUNT_STATUS.NEW_ACTIVE.value,
  ]
}

type FilterFTTHAccountTypeOptions = {
  '0_account': boolean
  '1_account': boolean
  '2_account': boolean
}

export const buildFilterFTTHAccountType = (
  options: FilterFTTHAccountTypeOptions,
  isAppliedFilter: boolean
): number[] => {
  const filterMap = {
    '0_account': FTTH_ACCOUNT_TYPE.NO_ACCOUNT.value,
    '1_account': FTTH_ACCOUNT_TYPE.ONE_ACCOUNT.value,
    '2_account': FTTH_ACCOUNT_TYPE.TWO_ACCOUNT.value,
  } as const

  if (isAppliedFilter) {
    return (
      Object.entries(options)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        .filter(([_, isSelected]) => isSelected)
        .map(([key]) => filterMap[key as keyof FilterFTTHAccountTypeOptions])
    )
  }

  return [
    FTTH_ACCOUNT_TYPE.NO_ACCOUNT.value,
    FTTH_ACCOUNT_TYPE.ONE_ACCOUNT.value,
    FTTH_ACCOUNT_TYPE.TWO_ACCOUNT.value,
  ]
}

// if this arrays contain user shop id value => USER TYPE = VTC
export const SHOP_IDs = [61]

export const DEFAULT_FILTER: FormFilterValues = {
  '0_account': false,
  '1_account': false,
  '2_account': false,
  block_1_m: false,
  block_2_m: false,
  block_3_m: false,
  terminate_block_over_3_m: false,
  new_active: false,
  less_3_m: false,
  less_6_m: false,
  less_12_m: false,
  over_12_m: false,
}
