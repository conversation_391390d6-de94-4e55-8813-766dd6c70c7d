import { IAccountFTTH, ICableBox, IProvince } from '@/types'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

interface ViewMapState {
  currentLocation: {
    latitude: number
    longitude: number
  } | null
  province: {
    listProvince: IProvince[]
    loading: boolean
    error: string | null
  }
  cableBox: {
    listCableBox: ICableBox[]
    loading: boolean
    error: string | null
  }
  accountFTTH: {
    listAccountFTTH: IAccountFTTH[]
    loading: boolean
    error: string | null
  }
  setCurrentLocation: (location: ViewMapState['currentLocation']) => void
  initializeCurrentLocation: () => void
}

const useViewMapStore = create<ViewMapState>()(
  devtools<ViewMapState>((set) => ({
    currentLocation: null,
    province: {
      listProvince: [],
      loading: false,
      error: null,
    },
    cableBox: {
      listCableBox: [],
      loading: false,
      error: null,
    },
    accountFTTH: {
      listAccountFTTH: [],
      loading: false,
      error: null,
    },
    setCurrentLocation: (location: ViewMapState['currentLocation']) => {
      console.log(location)
      set({ currentLocation: location })
    },
    initializeCurrentLocation: () => {
      set({ currentLocation: null })
    },
  }))
)

export default useViewMapStore
