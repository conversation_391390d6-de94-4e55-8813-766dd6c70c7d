import { login } from '@/api'
import {
  IS_AUTHENTICATED,
  LAST_INPUT_LOCATION,
  LAST_SELECTED_PROVINCE,
  TOKEN,
  USER,
} from '@/constants'
import { ERROR_CODE } from '@/constants/error'
import { IUserStore } from '@/types'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

interface AuthState {
  user: IUserStore | null
  token: string | null
  loading: boolean
  error: string | null
  isAuthenticated: boolean
  isTokenExpired: boolean
  login: (
    email: string,
    password: string
  ) => Promise<{
    message: string
    isLoggedIn: boolean
  }>
  logout: () => void
  initializeAuth: () => void
  checkTokenExpiration: () => void
}

const useAuthStore = create<AuthState>()(
  devtools<AuthState>((set) => ({
    user: null,
    token: null,
    loading: false,
    error: null,
    isAuthenticated: false,
    isTokenExpired: false,
    login: async (email, password) => {
      set({ loading: true, error: null })
      try {
        const { data } = await login(email, password)

        if (data.wsResponse?.token && data.wsResponse?.userInfo) {
          const token = data.wsResponse.token

          set({
            user: {
              ...data.wsResponse.userInfo,
              shopId: data.wsResponse.shopId,
              userProvince: data.wsResponse.userProvince,
            },
            token,
            loading: false,
            isAuthenticated: true,
          })

          localStorage.setItem(TOKEN, token)
          localStorage.setItem(IS_AUTHENTICATED, 'true')
          localStorage.setItem(
            USER,
            JSON.stringify({
              userId: data.wsResponse.userInfo.userId,
              username: data.wsResponse.userInfo.username,
              fullName: data.wsResponse.userInfo.fullName,
              email: data.wsResponse.userInfo.email,
              deptLevel: data.wsResponse.userInfo.deptLevel,
              shopId: data.wsResponse.shopId,
              userProvince: data.wsResponse.userProvince,
            })
          )

          return Promise.resolve({
            message: 'Login successfully',
            isLoggedIn: true,
          })
        }

        if (data.errorCode === ERROR_CODE.ERROR_CODE_1) {
          return Promise.reject({
            message: data.message,
            isLoggedIn: false,
          })
        }

        return Promise.reject({
          isLoggedIn: false,
        })

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (err: any) {
        set({ error: err.message, loading: false })
        return Promise.reject(err)
      } finally {
        set({ loading: false })
      }
    },

    logout: () => {
      set({
        user: null,
        isAuthenticated: false,
        token: null,
        error: null,
        loading: false,
        isTokenExpired: false,
      })
      localStorage.removeItem(IS_AUTHENTICATED)
      localStorage.removeItem(TOKEN)
      localStorage.removeItem(USER)
      localStorage.removeItem(LAST_SELECTED_PROVINCE)
      localStorage.removeItem(LAST_INPUT_LOCATION)
    },

    initializeAuth: () => {
      const token = localStorage.getItem(TOKEN)
      const user = localStorage.getItem(USER)
      const isAuth = localStorage.getItem(IS_AUTHENTICATED)

      if (token && user && isAuth) {
        set({
          user: JSON.parse(user),
          token,
          isAuthenticated: isAuth === 'true',
        })
      }
    },
    checkTokenExpiration: () => {
      set({ isTokenExpired: true })
    },
  }))
)

export default useAuthStore
