import {
  buildFilterCableBox,
  buildFilterFTTHAccountType,
  buildFilterFTTHStatus,
} from '@/constants/view-map'
import { IAccountFTTH, ICableBox } from '@/types'
import { useEffect, useState } from 'react'
import type { FormFilterValues } from '../components/Filter'

type Poi = { key: string; location: google.maps.LatLngLiteral; status: number; type?: number }

export const useMarkers = (
  mapBounds: google.maps.LatLngBounds | null,
  cableBoxPoints: Poi[],
  ftthAccountPoints: Poi[],
  formFilter: FormFilterValues
) => {
  const [visibleCableBoxLocations, setVisibleCableBoxLocations] = useState<Poi[]>([])
  const [visibleFtthAccountLocations, setVisibleFtthAccountLocations] = useState<Poi[]>([])
  const [selectedCableMarker, setSelectedCableMarker] = useState<ICableBox | null>(null)
  const [selectedFtthMarker, setSelectedFtthMarker] = useState<IAccountFTTH | null>(null)

  const isInViewport = (location: google.maps.LatLngLiteral, bounds: google.maps.LatLngBounds) => {
  const ne = bounds.getNorthEast()
  const sw = bounds.getSouthWest()
  return location.lat >= sw.lat() && location.lat <= ne.lat() &&
         location.lng >= sw.lng() && location.lng <= ne.lng()
}

  useEffect(() => {
    if (!mapBounds) {
      setVisibleCableBoxLocations([])
      return
    }

    const isAppliedFilter = Object.values(formFilter).some((value) => Boolean(value))

    const buildedCableBoxFilter = buildFilterCableBox(
      {
        less_3_m: formFilter.less_3_m,
        less_6_m: formFilter.less_6_m,
        less_12_m: formFilter.less_12_m,
        over_12_m: formFilter.over_12_m,
      },
      isAppliedFilter
    )

    const filteredLocations = cableBoxPoints.filter(
      (p) =>
        isInViewport(p.location, mapBounds) &&
        buildedCableBoxFilter.includes(p.status)
    )

    setVisibleCableBoxLocations(filteredLocations)
  }, [mapBounds, cableBoxPoints, formFilter])

  useEffect(() => {
    if (!mapBounds) {
      setVisibleFtthAccountLocations([])
      return
    }

    const isAppliedFilter = Object.values(formFilter).some((value) => Boolean(value))

    const buildedFTTHFilter = buildFilterFTTHAccountType(
      {
        '0_account': formFilter['0_account'],
        '1_account': formFilter['1_account'],
        '2_account': formFilter['2_account'],
      },
      isAppliedFilter
    )

    const buildedFTTHStatusFilter = buildFilterFTTHStatus(
      {
        block_1_m: formFilter.block_1_m,
        block_2_m: formFilter.block_2_m,
        block_3_m: formFilter.block_3_m,
        terminate_block_over_3_m: formFilter.terminate_block_over_3_m,
        new_active: formFilter.new_active,
      },
      isAppliedFilter
    )

    const filteredLocations = ftthAccountPoints.filter(
      (p) =>
        isInViewport(p.location, mapBounds) &&
        buildedFTTHFilter.includes(Number(p.type) || 0) &&
        buildedFTTHStatusFilter.includes(p.status)
    )

    setVisibleFtthAccountLocations(filteredLocations)
  }, [mapBounds, ftthAccountPoints, formFilter])

  return {
    visibleCableBoxLocations,
    visibleFtthAccountLocations,
    selectedCableMarker,
    setSelectedCableMarker,
    selectedFtthMarker,
    setSelectedFtthMarker,
    setVisibleCableBoxLocations,
    setVisibleFtthAccountLocations,
  }
}
