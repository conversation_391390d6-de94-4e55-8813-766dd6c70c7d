/* eslint-disable @typescript-eslint/no-explicit-any */
import Select, { components, MenuListProps } from 'react-select'

export type OptionType = {
  value: string
  label: string
}

type CustomSelectProps = {
  options: OptionType[]
  value: OptionType | undefined
  onChange: (selected: OptionType | null) => void
  placeholder?: string
  isDisabled?: boolean
  isClearable?: boolean
  loading?: boolean
}

const CustomMenuList: React.FC<MenuListProps<any, boolean>> = (props) => (
  <components.MenuList {...props}>{props.children}</components.MenuList>
)

const customStyles = {
  control: (base: any, state: any) => ({
    ...base,
    backgroundColor: state.isDisabled ? 'var(--color-neutral-200)' : 'white',
    borderColor: state.isFocused ? 'var(--color-primary)' : '#d1d5db', // blue-500 or gray-300
    boxShadow: state.isFocused ? '0 0 0 1px var(--color-primary)' : 'none',
    '&:hover': { borderColor: state.isDisabled ? '#d1d5db' : 'var(--color-primary)' },
    borderRadius: '0.5rem',
    minHeight: '40px',
    cursor: state.isDisabled ? 'not-allowed' : 'pointer',
    opacity: state.isDisabled ? 0.6 : 1,
  }),
  option: (base: any, state: any) => ({
    ...base,
    backgroundColor: state.isSelected
      ? 'var(--color-primary)'
      : state.isFocused
        ? '#e0f2fe'
        : 'white',
    color: state.isSelected ? 'white' : '#111827',
    cursor: 'pointer',
    height: 40,
    lineHeight: '25px',
  }),
  menu: (base: any) => ({
    ...base,
    borderRadius: '0.5rem',
    zIndex: 50,
    overflow: 'hidden',
  }),
  indicatorSeparator: () => ({
    display: 'none',
  }),
  dropdownIndicator: (base: any, state: any) => ({
    ...base,
    color: state.isFocused ? 'var(--color-primary)' : '#000000',
    padding: '0 8px',
  }),
  menuList: (base: any) => ({
    ...base,
    paddingTop: 0,
    paddingBottom: 0,
    maxHeight: '250px',
    overflowY: 'scroll',
    marginRight: '0px',
  }),
}

const CustomSelect = ({
  options,
  value,
  onChange,
  placeholder = 'Select...',
  isDisabled = false,
  isClearable = false,
  loading = false,
}: CustomSelectProps) => {
  return (
    <Select
      className="w-full"
      components={{ MenuList: CustomMenuList }}
      styles={customStyles}
      options={options}
      value={value}
      onChange={(newValue) => onChange(newValue as OptionType | null)}
      placeholder={placeholder}
      isDisabled={isDisabled}
      isClearable={isClearable}
      isLoading={loading}
      menuPosition="fixed"
      menuShouldScrollIntoView={false}
    />
  )
}

export default CustomSelect
