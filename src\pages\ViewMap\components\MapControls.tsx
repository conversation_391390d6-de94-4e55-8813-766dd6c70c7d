import Button, { ButtonSize, ButtonVariant } from '@/components/Button'
import { IconCheck, IconPencil, IconSync, IconX } from '@/icons'
import { memo, useCallback, useRef } from 'react'
import type { FormFilterValues } from './Filter'
import Filter from './Filter'

interface MapControlsProps {
  isCheck: boolean
  radius: number
  maxRadius: number
  setRadius: (value: number) => void
  setIsCheck: (value: boolean) => void
  setIsOpen: (value: boolean) => void
  handleLoadChunkData: () => void
  onFilter: (values: FormFilterValues) => number
  onOpenCircle: () => void
  onOpenExportDataModel: () => void
}

export const MapControls = memo(
  ({
    isCheck,
    radius,
    maxRadius,
    setIsCheck,
    handleLoadChunkData,
    onFilter,
    setRadius,
    onOpenCircle,
    onOpenExportDataModel,
  }: MapControlsProps) => {
    const rafRef = useRef<number | null>(null)

    const updateRadius = useCallback(
      (newRadius: number) => {
        if (rafRef.current) cancelAnimationFrame(rafRef.current)
        rafRef.current = requestAnimationFrame(() => {
          setRadius(newRadius)
        })
      },
      [setRadius]
    )

    const handleRadiusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      updateRadius(Number(e.target.value))
    }
    if (isCheck) {
      return (
        <>
          <div className="absolute top-30 right-10 w-fit h-fit z-[100] flex flex-col gap-2">
            <Button className="cursor-pointer" rounded="circle" onClick={onOpenExportDataModel}>
              <IconCheck />
            </Button>
            <Button
              className="cursor-pointer"
              variant={ButtonVariant.DANGER}
              rounded="circle"
              onClick={() => {
                setIsCheck(false)
              }}
            >
              <IconX />
            </Button>
          </div>
          <div className="absolute w-1/2 bottom-2 left-1/2 transform -translate-x-1/2 p-2 rounded z-[101]">
            <input
              type="range"
              min="100"
              max={maxRadius}
              value={radius}
              onChange={handleRadiusChange}
              className="w-full mt-1 appearance-none h-2 rounded bg-primary [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:shadow-md [&::-webkit-slider-thumb]:cursor-pointer"
            />
          </div>
        </>
      )
    }

    return (
      <div className="absolute top-[80px] right-0 p-4">
        <div className="flex gap-4">
          <Button
            rounded="circle"
            onClick={() => {
              onOpenCircle()
            }}
          >
            <IconPencil />
          </Button>
          <Button
            size={ButtonSize.SM}
            variant={ButtonVariant.OUTLINE_NEUTRAL}
            rounded="full"
            onClick={handleLoadChunkData}
            shadow
            icon={<IconSync />}
          >
            Sync Data
          </Button>
          <Filter onSubmitFilter={onFilter} />
        </div>
      </div>
    )
  }
)
