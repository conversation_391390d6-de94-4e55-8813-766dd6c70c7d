import { IS_AUTHENTICATED } from '@/constants'
import ROUTE from '@/constants/route'

import { createFileRoute, FileRoutesByPath, redirect } from '@tanstack/react-router'
import { lazy } from 'react'

const Login = lazy(() => import('@/pages/Login'))

export const Route = createFileRoute(ROUTE.LOGIN as keyof FileRoutesByPath)({
  beforeLoad: () => {
    const isAuthenticated = localStorage.getItem(IS_AUTHENTICATED)
    if (isAuthenticated) {
      throw redirect({
        to: ROUTE.VIEW_MAP,
      })
    }
  },
  component: Login,
})
