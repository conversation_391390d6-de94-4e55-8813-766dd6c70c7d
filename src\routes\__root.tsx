import { useLogoutModal } from '@/hooks/useLogoutModal'
import Header from '@/layout/Header'
import useAuthStore from '@/stores/authStore'
import { createRootRoute, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { useEffect } from 'react'

const RootComponent = () => {
  const initializeAuth = useAuthStore((state) => state.initializeAuth)
  const isTokenExpired = useAuthStore((state) => state.isTokenExpired)

  const { LogoutModal, openModal } = useLogoutModal()

  useEffect(() => {
    initializeAuth()
  }, [initializeAuth])

  useEffect(() => {
    if (isTokenExpired) {
      openModal()
    }
  }, [isTokenExpired, openModal])

  return (
    <>
      <div className="flex flex-col h-screen">
        <Header />
        <main className="flex-1 bg-white overflow-auto">
          <Outlet />
          <LogoutModal children />
        </main>
      </div>
      <TanStackRouterDevtools />
    </>
  )
}

export const Route = createRootRoute({
  component: RootComponent,
})
