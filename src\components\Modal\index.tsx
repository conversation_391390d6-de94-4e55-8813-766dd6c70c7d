// components/Modal.tsx
import * as Dialog from '@radix-ui/react-dialog'

interface ModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  children: React.ReactNode
  contentClassName?: string
}

export default function Modal({
  open,
  onOpenChange,
  title,
  children,
  contentClassName,
}: ModalProps) {
  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[999]" />
        <Dialog.Content
          className={`fixed z-[9999999] left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl p-6 w-full max-w-md shadow-lg ${contentClassName}`}
        >
          <div className="flex justify-between items-center mb-4">
            <Dialog.Title className="text-lg font-semibold">{title ?? ''}</Dialog.Title>
            <Dialog.Close asChild>
              <button className="text-neutral-500 hover:text-neutral-700">
                {/* <X size={20} /> */}
              </button>
            </Dialog.Close>
          </div>
          <div>{children}</div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}
