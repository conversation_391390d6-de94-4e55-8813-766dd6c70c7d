import { useEffect, useRef, useState } from 'react'

export type OptionType = {
  value: string
  label: string
}

type MultiSelectProps = {
  options: OptionType[]
  value: OptionType[]
  onChange: (selected: OptionType[]) => void
  placeholder?: string
  isDisabled?: boolean
  isClearable?: boolean
  isSearchable?: boolean
  loading?: boolean
}

const MultiSelect = ({
  options,
  value = [],
  onChange,
  placeholder = 'Select...',
  isDisabled = false,
  isClearable = false,
  loading = false,
  isSearchable = false,
}: MultiSelectProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const selectRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  const toggleOption = (option: OptionType) => {
    const isSelected = value.some((item) => item.value === option.value)
    if (isSelected) {
      onChange(value.filter((item) => item.value !== option.value))
    } else {
      onChange([...value, option])
    }
  }

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange([])
  }

  const displayValue = value.map((v) => v.label).join(', ')

  return (
    <div className="relative w-full max-w-[285px]" ref={selectRef}>
      {/* Control */}
      <div
        className={`flex items-center justify-between p-2 border-b border-gray-300 min-h-[40px] max-h-[40px] cursor-pointer ${isDisabled || loading ? 'bg-gray-100 cursor-not-allowed opacity-60' : 'bg-white hover:border-primary'} ${isOpen ? 'border-primary ring-1 ring-primary rounded-md' : ''}`}
        onClick={() => !isDisabled && !loading && setIsOpen(!isOpen)}
      >
        <div className="flex-1 truncate">
          {value.length > 0 ? (
            <span className="text-gray-800">{displayValue}</span>
          ) : (
            <span className="text-gray-400">{placeholder}</span>
          )}
        </div>

        <div className="flex items-center gap-1 ml-2">
          {isClearable && value.length > 0 && !isDisabled && !loading && (
            <button
              type="button"
              onClick={clearSelection}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          )}
          {loading ? (
            <div className="w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
          ) : (
            <svg
              className={`w-4 h-4 text-primary transition-transform`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={4}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          )}
        </div>
      </div>

      {/* Dropdown Menu */}
      {isOpen && !isDisabled && !loading && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          {/* Search Input */}
          {isSearchable && (
            <div className="p-2 border-b">
              <input
                type="text"
                ref={inputRef}
                className="w-full p-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus
              />
            </div>
          )}

          {/* Options List */}
          <div className="py-1 overflow-auto max-h-[250px]">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => {
                const isSelected = value.some((item) => item.value === option.value)
                return (
                  <div
                    key={option.value}
                    className={`flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-blue-50`}
                    onClick={() => toggleOption(option)}
                  >
                    <div
                      className={`w-5 h-5 flex items-center justify-center border rounded transition-colors duration-200 ${isSelected ? 'bg-primary border-primary' : 'bg-white border-gray-300'}`}
                    >
                      {isSelected && (
                        <svg
                          className="w-4 h-4 text-white"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth={3}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      )}
                    </div>
                    <span className="truncate">{option.label}</span>
                  </div>
                )
              })
            ) : (
              <div className="px-3 py-2 text-gray-500">No options found</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default MultiSelect
