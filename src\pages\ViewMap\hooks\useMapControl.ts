import { useCallback, useRef, useState } from 'react'

const CAMBODIA_CENTER = { lat: 12.565679, lng: 104.990963 }

export const useMapControl = () => {
  const mapRef = useRef<google.maps.Map | null>(null)
  const [mapType, setMapType] = useState<google.maps.MapTypeId>('roadmap' as google.maps.MapTypeId)
  const [mapBounds, setMapBounds] = useState<google.maps.LatLngBounds | null>(null)
  const [circleCenter, setCircleCenter] = useState({
    lat: CAMBODIA_CENTER.lat,
    lng: CAMBODIA_CENTER.lng,
  })

  const handleIdle = useCallback((map: google.maps.Map | null) => {
    if (!map) return
    const bounds = map.getBounds()
    if (bounds) {
      setMapBounds(bounds)
    }
  }, [])

  const switchType = useCallback((type: google.maps.MapTypeId) => {
    setMapType(type)
    mapRef.current?.setMapTypeId(type)
  }, [])

  return {
    mapRef,
    mapType,
    mapBounds,
    circleCenter,
    setCircleCenter,
    handleIdle,
    switchType,
    CAMBODIA_CENTER,
    mapBoundsDetails: {
      minLat: mapBounds?.getSouthWest().lat(),
      maxLat: mapBounds?.getNorthEast().lat(),
      minLng: mapBounds?.getSouthWest().lng(),
      maxLng: mapBounds?.getNorthEast().lng(),
    },
  }
}
