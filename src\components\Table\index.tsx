import Pagination from '../Pagination'
import CustomSelect from '../Select'
import Spinner from '../Spinner'

export interface Column<T> {
  header: string
  accessor: keyof T
  width?: string
  render?: (value: T[keyof T], row: T) => React.ReactNode
}

interface TableProps<T> {
  columns: Column<T>[]
  data: T[]
  rowsPerPageOptions?: number[]
  onPageChange?: (page: number) => void
  onRowsPerPageChange?: (rowsPerPage: number) => void
  currentPage?: number
  totalPages?: number
  rowsPerPage?: number
  header?: React.ReactNode
  loading?: boolean
  totalRecords?: number
}

export default function Table<T>({
  columns,
  data,
  rowsPerPageOptions = [10, 20, 50],
  onPageChange,
  onRowsPerPageChange,
  currentPage = 1,
  totalPages = 1,
  rowsPerPage = 10,
  header,
  loading = false,
  totalRecords,
}: TableProps<T>) {
  return (
    <div className="w-full">
      <div className="relative overflow-x-auto max-h-[700px] overflow-y-auto">
        <table className="w-full border-collapse border border-table-border">
          {header ? (
            <thead>{header}</thead>
          ) : (
            <thead>
              <tr className="bg-neutral-100">
                {columns.map((column, index) => (
                  <th
                    key={index}
                    className="p-4 text-center text-sm font-bold text-gray-700 border border-table-border"
                    style={{ width: column.width }}
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
          )}
          {loading ? (
            <tbody>
              <tr>
                <td
                  colSpan={columns.length}
                  className="p-4 text-sm text-gray-600 border border-table-border"
                >
                  <div className="w-full flex justify-center items-center">
                    <Spinner />
                  </div>
                </td>
              </tr>
            </tbody>
          ) : (
            <tbody>
              {data.length > 0 ? (
                data.map((row, rowIndex) => (
                  <tr key={rowIndex} className={`border-b hover:bg-table-row-hover bg-white`}>
                    {columns.map((column, colIndex) => (
                      <td
                        key={colIndex}
                        className="p-4 text-sm text-gray-600 border border-table-border"
                      >
                        {column.render ? (
                          column.render(row[column.accessor], row)
                        ) : (
                          <div className="flex items-center justify-between gap-2">
                            {String(row[column.accessor])}
                          </div>
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="p-4 text-sm text-gray-600 border border-table-border text-center"
                  >
                    No data found
                  </td>
                </tr>
              )}
            </tbody>
          )}
        </table>
      </div>

      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Rows per page</span>
          <div>
            <CustomSelect
              options={rowsPerPageOptions.map((option) => ({
                value: String(option),
                label: String(option),
              }))}
              value={{ value: String(rowsPerPage), label: String(rowsPerPage) }}
              onChange={(selected) => selected && onRowsPerPageChange?.(Number(selected.value))}
              placeholder="Rows per page"
              isClearable={false}
            />
          </div>
          <span className="text-sm text-gray-600">
            {`${(currentPage - 1) * rowsPerPage + 1} - ${Math.min(
              currentPage * rowsPerPage,
              totalRecords ?? 0
            )} of ${totalRecords}`}
          </span>
        </div>

        <Pagination
          currentPage={currentPage}
          pageCount={totalPages}
          onPageChange={({ selected }) => onPageChange?.(selected + 1)}
        />
      </div>
    </div>
  )
}
