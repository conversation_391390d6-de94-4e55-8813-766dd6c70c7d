import { getListProvince, getViewMapReportByProvince } from '@/api'
import Button, { ButtonVariant } from '@/components/Button'
import { OptionType } from '@/components/Select'
import Table, { Column } from '@/components/Table'
import Tooltip from '@/components/Tooltip'
import { EyeIcon, IconExport, IconLookUp } from '@/icons'
import {
  EViewMapReportCableBoxStatus,
  EViewMapReportStatusFTTH,
  EViewMapReportType,
  IViewMapReport,
} from '@/types'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo, useState } from 'react'
import { columns } from './column'
import ExportCableBoxModal from './components/ExportCableBoxModal'
import ExportFTTHAccountModal from './components/ExportFTTHAccountModal'
import ExportListProvinceModal from './components/ExportListProvinceModal'
import MultiSelect from './components/SearchMultiple2'

const CustomHeader = () => {
  return (
    <>
      <tr className="bg-neutral-100">
        <th
          rowSpan={2}
          className="p-4 text-center text-sm font-bold text-gray-700 border border-table-border w-[200px]"
        >
          Province
        </th>
        <th
          colSpan={4}
          className="p-4 text-center text-sm font-bold text-gray-700 border border-table-border"
        >
          Usage time of Cable box
        </th>
        <th
          colSpan={5}
          className="p-4 text-center text-sm font-bold text-gray-700 border border-table-border"
        >
          Status of FTTH account
        </th>
      </tr>
      <tr className="bg-neutral-100">
        {columns.slice(1).map((column, index) => (
          <th
            key={index}
            className="p-4 text-center text-sm font-bold text-gray-700 border border-table-border"
            style={{ width: column.width }}
          >
            {column.header}
          </th>
        ))}
      </tr>
    </>
  )
}

export type ModalInfo = {
  province?: {
    code: string
    name: string
  }
  type?: EViewMapReportType
  status?: EViewMapReportCableBoxStatus | EViewMapReportStatusFTTH
}

function Report() {
  const [selectedOptions, setSelectedOptions] = useState<OptionType[]>([])
  const [selectedProvinceCodes, setSelectedProvinceCodes] = useState<string[]>([])
  const [isOpenExportListProvinceModal, setIsOpenExportListProvinceModal] = useState(false)
  const [isOpenExportCableBoxModal, setIsOpenExportCableBoxModal] = useState(false)
  const [isOpenExportFTTHAccountModal, setIsOpenExportFTTHAccountModal] = useState(false)

  const [modalInfo, setModalInfo] = useState<ModalInfo | null>(null)

  const [paginate, setPaginate] = useState({
    page: 1,
    pageSize: 10,
  })

  const { data: listProvinceData, isLoading: isLoadingProvince } = useQuery({
    queryKey: ['province'],
    queryFn: async () => await getListProvince(),
  })

  const { data: listReportData, isLoading: isLoadingReport } = useQuery({
    queryKey: [
      'report',
      {
        page: paginate.page - 1,
        limit: paginate.pageSize,
        province: selectedProvinceCodes.join(','),
      },
    ],
    queryFn: async () =>
      await getViewMapReportByProvince({
        page: paginate.page - 1,
        pageSize: paginate.pageSize,
        province: selectedProvinceCodes.join(','),
      }),
  })

  const {
    data: listReportDataExport,
    isLoading: isLoadingReportExport,
    refetch,
  } = useQuery({
    queryKey: [
      'report',
      {
        page: 0,
        province: selectedProvinceCodes.join(','),
      },
    ],
    queryFn: async () =>
      await getViewMapReportByProvince({
        page: 0,
        province: selectedProvinceCodes.join(','),
      }),
    enabled: false,
  })

  const options = useMemo(() => {
    return (
      listProvinceData?.data?.lstProvince?.map((item) => ({
        value: item.code,
        label: item.name,
      })) || []
    )
  }, [listProvinceData])

  const getProvinceName = useCallback(
    (code: string) => {
      const province = listProvinceData?.data?.lstProvince?.find((item) => item.code === code)
      return province?.name || ''
    },
    [listProvinceData?.data?.lstProvince]
  )

  const columns: Array<
    Column<
      IViewMapReport & {
        provinceCode: string
      }
    >
  > = useMemo(
    () => [
      {
        header: 'Province',
        accessor: 'province',
        width: '200px',
      },
      {
        header: '< 3 months',
        accessor: 'reportCablebox' as keyof IViewMapReport,
        render: (value, row) => {
          return (
            <div className="flex items-center justify-between gap-2">
              {typeof value === 'object' && 'lt3Months' in value ? value.lt3Months : ''}
              <Tooltip content="Details" position="top">
                <Button
                  variant={ButtonVariant.OUTLINE_NEUTRAL}
                  rounded="circle"
                  onClick={() => {
                    setModalInfo({
                      province: {
                        code: row?.provinceCode,
                        name: row.province,
                      },
                      type: EViewMapReportType.CABLE_BOX,
                      status: EViewMapReportCableBoxStatus.LESS_3_MONTHS,
                    })
                    setIsOpenExportCableBoxModal(true)
                  }}
                >
                  <EyeIcon />
                </Button>
              </Tooltip>
            </div>
          )
        },
      },
      {
        header: '< 6 months',
        accessor: 'reportCablebox' as keyof IViewMapReport,
        render: (value, row) => (
          <div className="flex items-center justify-between gap-2">
            {typeof value === 'object' && 'lt6Months' in value ? value.lt6Months : ''}
            <Tooltip content="Details" position="top">
              <Button
                variant={ButtonVariant.OUTLINE_NEUTRAL}
                rounded="circle"
                onClick={() => {
                  setModalInfo({
                    province: {
                      code: row?.provinceCode,
                      name: row?.province,
                    },
                    type: EViewMapReportType.CABLE_BOX,
                    status: EViewMapReportCableBoxStatus.LESS_6_MONTHS,
                  })
                  setIsOpenExportCableBoxModal(true)
                }}
              >
                <EyeIcon />
              </Button>
            </Tooltip>
          </div>
        ),
      },
      {
        header: '< 12 months',
        accessor: 'reportCablebox',
        render: (value, row) => (
          <div className="flex items-center justify-between gap-2">
            {typeof value === 'object' && 'lt12Months' in value ? value.lt12Months : ''}
            <Tooltip content="Details" position="top">
              <Button
                variant={ButtonVariant.OUTLINE_NEUTRAL}
                rounded="circle"
                onClick={() => {
                  setModalInfo({
                    province: {
                      code: row?.provinceCode,
                      name: row?.province,
                    },
                    type: EViewMapReportType.CABLE_BOX,
                    status: EViewMapReportCableBoxStatus.LESS_12_MONTHS,
                  })
                  setIsOpenExportCableBoxModal(true)
                }}
              >
                <EyeIcon />
              </Button>
            </Tooltip>
          </div>
        ),
      },
      {
        header: '> 12 months',
        accessor: 'reportCablebox',
        render: (value, row) => (
          <div className="flex items-center justify-between gap-2">
            {typeof value === 'object' && 'gt12Months' in value ? value.gt12Months : ''}
            <Tooltip content="Details" position="top">
              <Button
                variant={ButtonVariant.OUTLINE_NEUTRAL}
                rounded="circle"
                onClick={() => {
                  setModalInfo({
                    province: {
                      code: row?.provinceCode,
                      name: row?.province,
                    },
                    type: EViewMapReportType.CABLE_BOX,
                    status: EViewMapReportCableBoxStatus.OVER_12_MONTHS,
                  })
                  setIsOpenExportCableBoxModal(true)
                }}
              >
                <EyeIcon />
              </Button>
            </Tooltip>
          </div>
        ),
      },
      {
        header: 'Newly activated',
        accessor: 'reportFtth',
        render: (value, row) => (
          <div className="flex items-center justify-between gap-2">
            {typeof value === 'object' && 'activeThisMonth' in value ? value.activeThisMonth : ''}
            <Tooltip content="Details" position="top">
              <Button
                variant={ButtonVariant.OUTLINE_NEUTRAL}
                rounded="circle"
                onClick={() => {
                  setModalInfo({
                    province: {
                      code: row?.provinceCode,
                      name: row?.province,
                    },
                    type: EViewMapReportType.FTTH_ACCOUNT,
                    status: EViewMapReportStatusFTTH.NEW_ACTIVE,
                  })
                  setIsOpenExportFTTHAccountModal(true)
                }}
              >
                <EyeIcon />
              </Button>
            </Tooltip>
          </div>
        ),
      },
      {
        header: 'Blocked for 1 month',
        accessor: 'reportFtth',
        render: (value, row) => (
          <div className="flex items-center justify-between gap-2">
            {typeof value === 'object' && 'block1Month' in value ? value.block1Month : ''}

            <Tooltip content="Details" position="top">
              <Button
                variant={ButtonVariant.OUTLINE_NEUTRAL}
                rounded="circle"
                onClick={() => {
                  setModalInfo({
                    province: {
                      code: row?.provinceCode,
                      name: row?.province,
                    },
                    type: EViewMapReportType.FTTH_ACCOUNT,
                    status: EViewMapReportStatusFTTH.BLOCK_1_MONTH,
                  })
                  setIsOpenExportFTTHAccountModal(true)
                }}
              >
                <EyeIcon />
              </Button>
            </Tooltip>
          </div>
        ),
      },
      {
        header: 'Blocked for 2 months',
        accessor: 'reportFtth',
        render: (value, row) => (
          <div className="flex items-center justify-between gap-2">
            {typeof value === 'object' && 'block2Month' in value ? value.block2Month : ''}

            <Tooltip content="Details" position="top">
              <Button
                variant={ButtonVariant.OUTLINE_NEUTRAL}
                rounded="circle"
                onClick={() => {
                  setModalInfo({
                    province: {
                      code: row?.provinceCode,
                      name: row?.province,
                    },
                    type: EViewMapReportType.FTTH_ACCOUNT,
                    status: EViewMapReportStatusFTTH.BLOCK_2_MONTHS,
                  })
                  setIsOpenExportFTTHAccountModal(true)
                }}
              >
                <EyeIcon />
              </Button>
            </Tooltip>
          </div>
        ),
      },
      {
        header: 'Blocked for 3 months',
        accessor: 'reportFtth',
        render: (value, row) => (
          <div className="flex items-center justify-between gap-2">
            {typeof value === 'object' && 'block3Month' in value ? value.block3Month : ''}
            <Tooltip content="Details" position="top">
              <Button
                variant={ButtonVariant.OUTLINE_NEUTRAL}
                rounded="circle"
                onClick={() => {
                  setModalInfo({
                    province: {
                      code: row?.provinceCode,
                      name: row?.province,
                    },
                    type: EViewMapReportType.FTTH_ACCOUNT,
                    status: EViewMapReportStatusFTTH.BLOCK_3_MONTHS,
                  })
                  setIsOpenExportFTTHAccountModal(true)
                }}
              >
                <EyeIcon />
              </Button>
            </Tooltip>
          </div>
        ),
      },
      {
        header: 'Blocked over 3 months',
        accessor: 'reportFtth',
        render: (value, row) => (
          <div className="flex items-center justify-between gap-2">
            {typeof value === 'object' && 'blockOver3Month' in value ? value.blockOver3Month : ''}

            <Tooltip content="Details" position="top">
              <Button
                variant={ButtonVariant.OUTLINE_NEUTRAL}
                rounded="circle"
                onClick={() => {
                  setModalInfo({
                    province: {
                      code: row?.provinceCode,
                      name: row?.province,
                    },
                    type: EViewMapReportType.FTTH_ACCOUNT,
                    status: EViewMapReportStatusFTTH.TERMINATE_BLOCK_OVER_3_MONTHS,
                  })
                  setIsOpenExportFTTHAccountModal(true)
                }}
              >
                <EyeIcon />
              </Button>
            </Tooltip>
          </div>
        ),
      },
    ],
    []
  )

  const reportTableData = useMemo(() => {
    return (
      listReportData?.data?.reports?.map((item) => ({
        ...item,
        province: getProvinceName(item.province),
        provinceCode: item.province,
      })) || []
    )
  }, [getProvinceName, listReportData?.data?.reports])

  const onSearch = useCallback(() => {
    const codes = selectedOptions.map((item) => item.value)
    setSelectedProvinceCodes(codes)
    setPaginate((prev) => ({ ...prev, page: 1 }))
  }, [selectedOptions])

  return (
    <div className="flex flex-1 h-full relative justify-center">
      <div className="py-6 w-full max-w-[1212px] flex flex-col gap-5">
        <p className="m-0 text-title-color text-3xl">REPORT</p>
        <div className="w-full flex justify-between items-center">
          <div className="flex w-1/3 items-end max-w-1/3">
            <div className="flex flex-col w-full gap-2">
              <span className="text-[#575F6E] text-base">Province</span>
              <MultiSelect
                options={options}
                value={selectedOptions}
                onChange={setSelectedOptions}
                placeholder={''}
                loading={isLoadingProvince}
              />
            </div>
            <Button className="ml-4 h-fit" icon={<IconLookUp />} onClick={onSearch}>
              Search
            </Button>
          </div>
          <Button
            variant={ButtonVariant.OUTLINE_PRIMARY}
            className="ml-4"
            onClick={async () => {
              await refetch()
              setIsOpenExportListProvinceModal(true)
            }}
            icon={<IconExport />}
            loading={isLoadingReportExport}
          >
            Export
          </Button>
        </div>
        <Table
          columns={columns}
          data={reportTableData}
          currentPage={paginate.page}
          totalPages={listReportData?.data.totalPages ?? 0}
          rowsPerPage={paginate.pageSize}
          onPageChange={(page) => setPaginate((prev) => ({ ...prev, page }))}
          onRowsPerPageChange={(rows) =>
            setPaginate((prev) => ({ ...prev, pageSize: rows, page: 1 }))
          }
          header={<CustomHeader />}
          loading={isLoadingReport}
          totalRecords={listReportData?.data.totalRecords ?? reportTableData.length}
        />
      </div>
      <ExportListProvinceModal
        isOpen={isOpenExportListProvinceModal}
        setIsOpen={setIsOpenExportListProvinceModal}
        data={
          listReportDataExport?.data?.reports?.map((item) => ({
            ...item,
            province: getProvinceName(item.province),
          })) || []
        }
      />
      <ExportCableBoxModal
        isOpen={isOpenExportCableBoxModal}
        onCloseModal={() => {
          setIsOpenExportCableBoxModal(false)
          setModalInfo(null)
        }}
        modalInfo={modalInfo}
      />
      <ExportFTTHAccountModal
        isOpen={isOpenExportFTTHAccountModal}
        onCloseModal={() => {
          setIsOpenExportFTTHAccountModal(false)
          setModalInfo(null)
        }}
        modalInfo={modalInfo}
      />
    </div>
  )
}

export default Report
