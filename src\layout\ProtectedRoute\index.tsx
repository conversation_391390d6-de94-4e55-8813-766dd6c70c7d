// src/components/ProtectedRoute.tsx
import useAuthStore from '@/stores/authStore'
import { Navigate, useLocation } from '@tanstack/react-router'

export const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const location = useLocation()
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  console.log('🚀 ~ ProtectedRoute ~ isAuthenticated:', isAuthenticated)

  if (!isAuthenticated) {
    // Redirect to login, saving the current location to return to after auth
    return <Navigate to="/login" search={{ redirect: location.href }} replace />
  }

  return <>{children}</>
}
