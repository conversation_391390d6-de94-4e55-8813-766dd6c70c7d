import Button, { ButtonVariant } from '@/components/Button'
import { Column } from '@/components/Table'
import { EyeIcon } from '@/icons'

export type RowData = {
  province: string
  less3months: number
  less6months: number
  less12months: number
  more12months: number
  newlyActivated: number
  blocked1month: number
  blocked2months: number
  blocked3months: number
  blockedOver3months: number
}

export const columns: Array<Column<RowData>> = [
  { header: 'Province', accessor: 'province', width: '200px' },
  {
    header: '< 3 months',
    accessor: 'less3months',
    render(value, row) {
      return (
        <div className="flex items-center justify-between gap-2">
          {value}
          <Button
            variant={ButtonVariant.OUTLINE_NEUTRAL}
            rounded="circle"
            onClick={() => {
              console.log(row)
            }}
          >
            <EyeIcon />
          </Button>
        </div>
      )
    },
  },
  {
    header: '< 6 months',
    accessor: 'less6months',
    render: (value, row) => (
      <div className="flex items-center justify-between gap-2">
        {value}
        <Button
          variant={ButtonVariant.OUTLINE_NEUTRAL}
          rounded="circle"
          onClick={() => {
            console.log(row)
          }}
        >
          <EyeIcon />
        </Button>
      </div>
    ),
  },
  {
    header: '< 12 months',
    accessor: 'less12months',
    render: (value, row) => (
      <div className="flex items-center justify-between gap-2">
        {value}
        <Button
          variant={ButtonVariant.OUTLINE_NEUTRAL}
          rounded="circle"
          onClick={() => {
            console.log(row)
          }}
        >
          <EyeIcon />
        </Button>
      </div>
    ),
  },
  {
    header: '> 12 months',
    accessor: 'more12months',
    render: (value, row) => (
      <div className="flex items-center justify-between gap-2">
        {value}
        <Button
          variant={ButtonVariant.OUTLINE_NEUTRAL}
          rounded="circle"
          onClick={() => {
            console.log(row)
          }}
        >
          <EyeIcon />
        </Button>
      </div>
    ),
  },
  {
    header: 'Newly activated',
    accessor: 'newlyActivated',
    render: (value, row) => (
      <div className="flex items-center justify-between gap-2">
        {value}
        <Button
          variant={ButtonVariant.OUTLINE_NEUTRAL}
          rounded="circle"
          onClick={() => {
            console.log(row)
          }}
        >
          <EyeIcon />
        </Button>
      </div>
    ),
  },
  {
    header: 'Blocked for 1 month',
    accessor: 'blocked1month',
    render: (value, row) => (
      <div className="flex items-center justify-between gap-2">
        {value}
        <Button
          variant={ButtonVariant.OUTLINE_NEUTRAL}
          rounded="circle"
          onClick={() => {
            console.log(row)
          }}
        >
          <EyeIcon />
        </Button>
      </div>
    ),
  },
  {
    header: 'Blocked for 2 months',
    accessor: 'blocked2months',
    render: (value, row) => (
      <div className="flex items-center justify-between gap-2">
        {value}
        <Button
          variant={ButtonVariant.OUTLINE_NEUTRAL}
          rounded="circle"
          onClick={() => {
            console.log(row)
          }}
        >
          <EyeIcon />
        </Button>
      </div>
    ),
  },
  {
    header: 'Blocked for 3 months',
    accessor: 'blocked3months',
    render: (value, row) => (
      <div className="flex items-center justify-between gap-2">
        {value}
        <Button
          variant={ButtonVariant.OUTLINE_NEUTRAL}
          rounded="circle"
          onClick={() => {
            console.log(row)
          }}
        >
          <EyeIcon />
        </Button>
      </div>
    ),
  },
  {
    header: 'Blocked over 3 months',
    accessor: 'blockedOver3months',
    render: (value, row) => (
      <div className="flex items-center justify-between gap-2">
        {value}
        <Button
          variant={ButtonVariant.OUTLINE_NEUTRAL}
          rounded="circle"
          onClick={() => {
            console.log(row)
          }}
        >
          <EyeIcon />
        </Button>
      </div>
    ),
  },
]
