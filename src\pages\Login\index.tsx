import logo from '@/../public/images/logo.png'
import loginFrame from '@/../public/svgs/login-frame-icon.svg'
import Button from '@/components/Button'
import Image from '@/components/Image'
import Input from '@/components/Input'
import ROUTE from '@/constants/route'
import { IconHalfArrowRight } from '@/icons'
import useAuthStore from '@/stores/authStore'
import { useNavigate } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'

type FormData = {
  username: string
  password: string
}

function Login() {
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      username: '',
      password: '',
    },
  })
  const navigate = useNavigate()
  const { login, loading } = useAuthStore()

  const onSubmit = async (data: FormData) => {
    try {
      const response = await login(data.username, data.password)

      if (response.isLoggedIn) {
        navigate({ to: ROUTE.VIEW_MAP })
        return
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: { message?: string } | any) {
      if (error?.message) {
        setError('username', { type: 'manual', message: error.message })
        setError('password', { type: 'manual', message: error.message })
      }
    }
  }

  return (
    <div className="flex flex-1 h-full relative">
      <div className="w-full h-full bg-[url('/svgs/login-background.svg')] bg-no-repeat bg-bottom bg-contain"></div>
      <div className="absolute top-0 left-0 w-full h-full bg-black/70">
        <div className="flex flex-col items-center justify-center h-full">
          <div className="flex flex-col w-full max-w-[1092px] mx-auto bg-[#D2F9F6] rounded-2xl shadow-2xl h-fit relative">
            <div className="flex justify-center items-center bg-white rounded-2xl shadow-lg px-[140px] py-[100px] w-fit">
              <div className="w-[394px] h-[434px] bg-white rounded-2xl shadow-xl flex flex-col items-center justify-center gap-8 p-8">
                <Image className="w-[234px]" src={logo} />
                <form onSubmit={handleSubmit(onSubmit)}>
                  <div className="flex flex-col items-center justify-center mt-4 w-full gap-4">
                    <p className="m-0 text-text-color text-sm">
                      Welcome back, Please login to your account!
                    </p>
                    <Input
                      className="w-full"
                      label="Username"
                      placeholder="Enter your username"
                      {...register('username', { required: 'Please enter your username.' })}
                      error={errors.username?.message}
                      isHiddenErrorMessage={
                        errors.password?.message ===
                        'Incorrect username or password. Please try again.'
                      }
                    />
                    <Input
                      className="w-full"
                      label="Password"
                      type="password"
                      placeholder="Enter your password"
                      {...register('password', { required: 'Please enter your password.' })}
                      error={errors.password?.message}
                    />
                    <Button
                      loading={loading}
                      className="w-full"
                      icon={<IconHalfArrowRight />}
                      iconPosition="right"
                    >
                      Sign In
                    </Button>
                  </div>
                </form>
              </div>
            </div>
            <div className="absolute right-[24px] -bottom-[20px]">
              <Image className="" src={loginFrame} />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
