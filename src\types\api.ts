export interface ApiResponse<T> {
  data: T
  message: string
  status: number
}

export interface ApiError {
  message: string
  status: number
  errors?: Record<string, string[]>
}

export interface IUserResponseInfo {
  userId: number
  userRight: number
  username: string
  password: string
  status: number
  email: string
  cellPhone: string
  gender: number
  identityCard: string
  fullName: string
  userTypeId: string
  createDate: string
  managerId: number
  passWordChange: number
  profileId: string
  lastResetPassword: string
  ip: string
  deptId: number
  deptLevel: string
  postId: number
  deptName: string
  ignoreCheckIp: number
  checkValidTime: number
  startTimeToChangePassword: string
  ipLan: string
  checkIp: number
  checkIpLan: number
  useSalt: number
  loginFailAllow: number
  temporaryLockTime: number
  maxTmpLockADay: number
  passwordValidTime: number
  userValidTime: number
  allowMultiIpLogin: number
  allowLoginTimeStart: number
  allowLoginTimeEnd: number
  id: number
  name: string
  needChangePassword: number
  timeToChangePassword: number
}

export type IUserStore = Pick<
  IUserResponseInfo,
  'userId' | 'username' | 'fullName' | 'email' | 'deptLevel'
> & {
  shopId: number
  userProvince: string
}

export interface ILoginResponse {
  errorCode: string
  message: string
  object: null
  userMsg: string | null
  wsResponse: {
    userInfo: IUserResponseInfo
    roles: string[] | null
    listMenuParent: string[] | null
    listMenuAccess: string[] | null
    listPermissionComponent: string[] | null
    token: string
    shopId: number
    userProvince: string
  }
}
