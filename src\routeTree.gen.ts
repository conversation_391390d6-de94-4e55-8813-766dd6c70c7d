/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as ViewMapImport } from './routes/view-map'
import { Route as ReportImport } from './routes/report'
import { Route as LoginImport } from './routes/login'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const ViewMapRoute = ViewMapImport.update({
  id: '/view-map',
  path: '/view-map',
  getParentRoute: () => rootRoute,
} as any)

const ReportRoute = ReportImport.update({
  id: '/report',
  path: '/report',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/report': {
      id: '/report'
      path: '/report'
      fullPath: '/report'
      preLoaderRoute: typeof ReportImport
      parentRoute: typeof rootRoute
    }
    '/view-map': {
      id: '/view-map'
      path: '/view-map'
      fullPath: '/view-map'
      preLoaderRoute: typeof ViewMapImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/report': typeof ReportRoute
  '/view-map': typeof ViewMapRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/report': typeof ReportRoute
  '/view-map': typeof ViewMapRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/report': typeof ReportRoute
  '/view-map': typeof ViewMapRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/login' | '/report' | '/view-map'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/login' | '/report' | '/view-map'
  id: '__root__' | '/' | '/login' | '/report' | '/view-map'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  LoginRoute: typeof LoginRoute
  ReportRoute: typeof ReportRoute
  ViewMapRoute: typeof ViewMapRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  LoginRoute: LoginRoute,
  ReportRoute: ReportRoute,
  ViewMapRoute: ViewMapRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/login",
        "/report",
        "/view-map"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/report": {
      "filePath": "report.tsx"
    },
    "/view-map": {
      "filePath": "view-map.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
