import { IS_AUTHENTICATED } from '@/constants'
import ROUTE from '@/constants/route'
import { createFileRoute, redirect } from '@tanstack/react-router'
import { lazy } from 'react'

const Home = lazy(() => import('@/pages/index'))

export const Route = createFileRoute('/')({
  beforeLoad: () => {
    const isAuthenticated = localStorage.getItem(IS_AUTHENTICATED)
    if (!isAuthenticated) {
      throw redirect({
        to: ROUTE.LOGIN,
      })
    } else {
      throw redirect({
        to: ROUTE.VIEW_MAP,
      })
    }
  },
  component: Home,
})
