/* eslint-disable @typescript-eslint/no-explicit-any */
import { saveAs } from 'file-saver'
import { useState } from 'react'
import * as XLSX from 'xlsx'
import Button, { ButtonProps } from '../Button'
import Spinner from '../Spinner'

type DataItem = Record<string, any>

interface ExcelColumn {
  header: string
  key: string
  width?: number
}

interface CustomerDataExporterProps<T extends DataItem> extends ButtonProps {
  data: T[]
  fileName?: string
  columns: ExcelColumn[]
  isReportViewMap?: boolean
}

const CustomerDataExporter = <T extends DataItem>({
  data,
  fileName = 'exported_data',
  columns,
  isReportViewMap = false,
  ...props
}: CustomerDataExporterProps<T>) => {
  const [isExporting, setIsExporting] = useState(false)

  // Format data for Excel export
  const formatData = (): Record<string, any>[] => {
    return data.map((item) => {
      const formattedRow: Record<string, any> = {}
      columns.forEach(({ header, key }) => {
        formattedRow[header] = item[key] ?? ''
      })
      return formattedRow
    })
  }

  // Export to Excel function
  const exportToExcel = async () => {
    try {
      setIsExporting(true)

      // Create workbook
      const wb = XLSX.utils.book_new()

      // Get headers from columns
      // const headers = columns.map((col) => col.header)

      // Convert data to worksheet
      // const ws = XLSX.utils.json_to_sheet(formatData(), { header: headers, skipHeader: true })

      // // Handle merged headers for Report View Map
      // if (isReportViewMap) {
      //   const mainHeaders = [
      //     [
      //       'Province',
      //       'Usage time of Cable box',
      //       '', // Empty for merged cells that will be covered
      //       '',
      //       '',
      //       'Status of FTTH account',
      //       '', // Empty for merged cells
      //       '',
      //       '',
      //       '',
      //     ],
      //     headers, // Original headers as second row
      //   ]

      //   // Clear and rebuild the worksheet
      //   ws['!ref'] = undefined
      //   XLSX.utils.sheet_add_aoa(ws, mainHeaders, { origin: 'A1' })
      //   XLSX.utils.sheet_add_json(ws, formatData(), {
      //     header: headers,
      //     skipHeader: true,
      //     origin: 'A3',
      //   })

      //   // Set column widths
      //   ws['!cols'] = columns.map((col) => ({ wch: col.width || 15 }))

      //   // Correct merge ranges
      //   ws['!merges'] = [
      //     // Province (merge vertically)
      //     { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } },
      //     // Subscription Duration (merge 4 columns horizontally)
      //     { s: { r: 0, c: 1 }, e: { r: 0, c: 4 } },
      //     // Block Status (merge 5 columns horizontally)
      //     { s: { r: 0, c: 5 }, e: { r: 0, c: 9 } },
      //   ]

      //   // Fix for J1 and J2 - ensure no accidental merging
      //   // Add explicit cell definitions for any problematic cells
      //   ws['J1'] = { t: 's', v: '' }
      //   ws['J2'] = { t: 's', v: headers[9] }
      // } else {
      //   // Normal export without merged headers
      //   XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A1' })
      //   ws['!cols'] = columns.map((col) => ({ wch: col.width || 15 }))
      // }

      const ws = XLSX.utils.json_to_sheet([]) // Initialize empty sheet

      if (isReportViewMap) {
        // Special Report View Map formatting
        const mainHeaders = [
          [
            'Province',
            'Usage time of Cable box',
            '',
            '',
            '',
            'Status of FTTH account',
            '',
            '',
            '',
            '',
          ],
          columns.map((col) => col.header),
        ]
        XLSX.utils.sheet_add_aoa(ws, mainHeaders, { origin: 'A1' })
        XLSX.utils.sheet_add_json(ws, formatData(), {
          skipHeader: true,
          origin: 'A3', // Data starts at row 3
        })

        // Set up merges as before
        ws['!merges'] = [
          { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } },
          { s: { r: 0, c: 1 }, e: { r: 0, c: 4 } },
          { s: { r: 0, c: 5 }, e: { r: 0, c: 9 } },
        ]
      } else {
        // Normal export - clearer header handling
        // Add headers as first row
        XLSX.utils.sheet_add_aoa(ws, [columns.map((col) => col.header)], { origin: 'A1' })

        // Add data starting from second row
        XLSX.utils.sheet_add_json(ws, formatData(), {
          skipHeader: true, // We've already added headers manually
          origin: 'A2', // Data starts at row 2
        })
      }
      // Set column widths for both cases
      ws['!cols'] = columns.map((col) => ({ wch: col.width || 15 }))

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, 'Data')

      // Generate Excel file
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })

      // Save file
      await saveAs(blob, `${fileName}.xlsx`)
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <Button onClick={exportToExcel} {...props} disabled={props.disabled || isExporting}>
      {isExporting ? (
        <div className="flex items-center">
          <Spinner />
          <span className="ml-2">Exporting...</span>
        </div>
      ) : (
        'Export'
      )}
    </Button>
  )
}

export default CustomerDataExporter
