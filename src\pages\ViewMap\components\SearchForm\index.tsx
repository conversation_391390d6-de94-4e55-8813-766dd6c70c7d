import { getListProvince } from '@/api'
import Button from '@/components/Button'
import CustomSelect, { Option } from '@/components/CustomSelect'
import Input from '@/components/Input'
import { LAST_INPUT_LOCATION, LAST_SELECTED_PROVINCE } from '@/constants'
import { SHOP_IDs } from '@/constants/view-map'
import provinceLocationLatLng from '@/data/province_lat_long.json'
import useRequestLocationPermission from '@/hooks/useRequestLocationPermission'
import { Route as ViewMapRoute } from '@/routes/view-map'
import useAuthStore from '@/stores/authStore'
import useViewMapStore from '@/stores/viewMapStore'
import { useQuery } from '@tanstack/react-query'
import { useNavigate, useSearch } from '@tanstack/react-router'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

function SearchForm({
  onSearch = () => {},
  handleZoomToProvince,
}: {
  onSearch: ({ lat, long }: { lat?: number; long?: number }) => void
  handleZoomToProvince: ({ latitude, longitude }: { latitude: number; longitude: number }) => void
}) {
  const { requestLocationPermission } = useRequestLocationPermission()
  const user = useAuthStore((state) => state.user)
  const currentLocation = useViewMapStore((state) => state.currentLocation)
  const [searchValue, setSearchValue] = useState({
    province: '',
    lat: '',
    long: '',
  })

  const btnRef = useRef<HTMLButtonElement | null>(null)
  const btnRequestRef = useRef<HTMLButtonElement | null>(null)

  const search = useSearch({
    from: ViewMapRoute.fullPath,
  })
  const navigate = useNavigate({
    from: ViewMapRoute.fullPath,
  })

  const { data: listProvinceData, isLoading: isLoadingProvince } = useQuery({
    queryKey: ['province'],
    queryFn: async () => await getListProvince(),
  })

  const options = useMemo(() => {
    return (
      listProvinceData?.data?.lstProvince?.map((item) => ({
        value: item.code,
        label: item.name,
      })) || []
    )
  }, [listProvinceData])

  useEffect(() => {
    setSearchValue({
      province: (search as { province?: string }).province || '',
      lat: (search as { lat?: number }).lat?.toString() || '',
      long: (search as { long?: number }).long?.toString() || '',
    })
  }, [search])

  useEffect(() => {
    const lastSelectedProvince = localStorage.getItem(LAST_SELECTED_PROVINCE)
    if (lastSelectedProvince) {
      setSearchValue((prev) => ({ ...prev, province: lastSelectedProvince }))
      navigate({
        search: (search) => ({
          ...search,
          province: lastSelectedProvince,
        }),
      })
      return
    }
    if (user?.shopId && !SHOP_IDs.includes(user?.shopId) && user?.userProvince) {
      setSearchValue((prev) => ({ ...prev, province: user?.userProvince }))
      navigate({
        search: (search) => ({
          ...search,
          province: user?.userProvince,
        }),
      })
    }
  }, [navigate, onSearch, user?.shopId, user?.userProvince])

  useEffect(() => {
    const lastInputLocation = localStorage.getItem(LAST_INPUT_LOCATION)
    if (lastInputLocation) {
      const { lat, long } = JSON.parse(lastInputLocation)
      setSearchValue((prev) => ({ ...prev, lat, long }))
      navigate({
        search: (search) => ({
          ...search,
          lat: lat ? Number(lat) : undefined,
          long: long ? Number(long) : undefined,
        }),
      })
      setTimeout(() => {
        btnRef.current?.click()
      }, 100)
      return
    }
    if (currentLocation) {
      setSearchValue((prev) => ({
        ...prev,
        lat: currentLocation.latitude.toString(),
        long: currentLocation.longitude.toString(),
      }))
      navigate({
        search: (search) => ({
          ...search,
          lat: currentLocation.latitude,
          long: currentLocation.longitude,
        }),
      })
      setTimeout(() => {
        btnRef.current?.click()
      }, 100)
    }
  }, [currentLocation, navigate])

  const handleSearch = () => {
    localStorage.setItem(
      LAST_INPUT_LOCATION,
      JSON.stringify({ lat: searchValue.lat, long: searchValue.long })
    )
    navigate({
      search: (search) => ({
        ...search,
        province: searchValue.province,
        lat: searchValue.lat ? Number(searchValue.lat) : undefined,
        long: searchValue.long ? Number(searchValue.long) : undefined,
      }),
    })
    if (!searchValue.lat || !searchValue.long) return
    onSearch({ lat: Number(searchValue.lat), long: Number(searchValue.long) })
  }

  useEffect(() => {
    btnRequestRef.current?.click()
  }, [])

  const onSelectChanged = useCallback(
    (value: Option | Option[] | null) => {
      const provinceLocation = provinceLocationLatLng.find(
        (item) => item.code === (Array.isArray(value) ? value[0]?.value : value?.value)
      )

      if (provinceLocation) {
        handleZoomToProvince({
          latitude: provinceLocation.latitude,
          longitude: provinceLocation.longitude,
        })
      }

      setSearchValue({
        ...searchValue,
        province: Array.isArray(value) ? value[0]?.value || '' : value?.value || '',
      })
      navigate({
        search: (search) => ({
          ...search,
          province: Array.isArray(value) ? value[0]?.value || '' : value?.value || '',
        }),
      })
      localStorage.setItem(
        LAST_SELECTED_PROVINCE,
        Array.isArray(value) ? value[0]?.value || '' : value?.value || ''
      )
    },
    [handleZoomToProvince, navigate, searchValue]
  )

  return (
    <>
      <div className="p-6 bg-white shadow-md rounded-lg flex flex-col gap-4 w-[448px] items-center">
        <div className="flex gap-4 w-full">
          <div className="w-[80px]">
            <p className="m-0 text-lg font-normal">Province</p>
          </div>
          <CustomSelect
            placeholder="--Select province--"
            options={options}
            value={options.find((item) => item.value === searchValue.province)}
            onChange={onSelectChanged}
            loading={isLoadingProvince}
            isDisabled={isLoadingProvince || !(user?.shopId && SHOP_IDs.includes(user?.shopId))}
          />
        </div>
        <div className="flex gap-4 w-full">
          <div className="w-[80px]">
            <p className="m-0 text-lg font-normal">Location</p>
          </div>
          <div className="flex flex-col gap-4 w-full">
            <Input
              className="border-[#A2A2A2] border-0 border-b-1 rounded-none focus:border-b-primary focus:outline-none !focus:ring-0 hover:border-b-primary placeholder:italic"
              name="lat"
              placeholder="Lat....."
              value={searchValue.lat}
              onChange={(e) => setSearchValue({ ...searchValue, lat: e.target.value })}
            ></Input>
            <Input
              name="long"
              className="border-[#A2A2A2] border-0 border-b-1 rounded-none focus:border-b-primary focus:outline-none !focus:ring-0 hover:border-b-primary placeholder:italic"
              placeholder="Long....."
              value={searchValue.long}
              onChange={(e) => setSearchValue({ ...searchValue, long: e.target.value })}
            ></Input>
          </div>
        </div>
        <Button className="w-1/2" onClick={handleSearch} ref={btnRef}>
          Update on map
        </Button>
        <button hidden ref={btnRequestRef} onClick={requestLocationPermission}></button>
      </div>
    </>
  )
}

export default SearchForm
